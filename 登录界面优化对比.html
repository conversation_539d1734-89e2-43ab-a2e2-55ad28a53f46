<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录界面优化对比</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            color: #1f2937;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: #10b981;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .header p {
            color: #6b7280;
            font-size: 16px;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .demo-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .demo-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            padding: 15px;
            border-radius: 8px;
        }

        .old-title {
            background: #fee2e2;
            color: #dc2626;
        }

        .new-title {
            background: #dcfce7;
            color: #166534;
        }

        /* 旧版样式模拟 */
        .old-login-form {
            max-width: 350px;
            margin: 0 auto;
            padding: 40px 30px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .old-input {
            width: 100%;
            height: 54px;
            border-radius: 12px;
            border: 2px solid #f0f2f5;
            padding: 0 16px 0 50px;
            font-size: 15px;
            background-color: #fafbfc;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            transition: all 0.3s ease;
        }

        .old-input:focus {
            border-color: #4facfe;
            background-color: #ffffff;
            box-shadow: 0 4px 12px rgba(79, 172, 254, 0.15);
            outline: none;
        }

        .old-btn {
            width: 100%;
            height: 54px;
            border-radius: 12px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4facfe 100%);
            border: none;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 6px 20px rgba(30, 60, 114, 0.3);
            transition: all 0.3s ease;
        }

        .old-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 60, 114, 0.4);
        }

        /* 新版样式 */
        .new-login-form {
            max-width: 350px;
            margin: 0 auto;
            padding: 40px 30px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .new-input {
            width: 100%;
            height: 50px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 0 16px 0 50px;
            font-size: 15px;
            background-color: #ffffff;
            margin-bottom: 20px;
            transition: all 0.2s ease;
        }

        .new-input:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            outline: none;
        }

        .new-btn {
            width: 100%;
            height: 50px;
            border-radius: 8px;
            background: #10b981;
            border: none;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .new-btn:hover {
            background: #059669;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .input-wrapper {
            position: relative;
            margin-bottom: 20px;
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #10b981;
            font-size: 16px;
            z-index: 1;
        }

        .old-input-icon {
            color: #1e3c72;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: #10b981;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 36px;
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.2);
        }

        .old-logo {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4facfe 100%);
            border-radius: 24px;
            width: 90px;
            height: 90px;
            box-shadow: 0 12px 30px rgba(30, 60, 114, 0.3);
        }

        .form-title {
            text-align: center;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #1f2937;
        }

        .form-subtitle {
            text-align: center;
            font-size: 15px;
            color: #6b7280;
            margin-bottom: 32px;
        }

        .improvements {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-top: 30px;
        }

        .improvements h3 {
            color: #10b981;
            margin-bottom: 20px;
            font-size: 24px;
            text-align: center;
        }

        .improvement-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .improvement-item {
            padding: 20px;
            background: #f8fffe;
            border-radius: 12px;
            border-left: 4px solid #10b981;
        }

        .improvement-item h4 {
            color: #047857;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .improvement-item p {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.5;
        }

        .before-after {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }

        .before, .after {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .before {
            background: #fee2e2;
            color: #dc2626;
        }

        .after {
            background: #dcfce7;
            color: #166534;
        }

        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .container {
                padding: 10px;
            }
            
            .improvement-list {
                grid-template-columns: 1fr;
            }
        }

        .demo-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .new-demo-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 登录界面现代化优化</h1>
            <p>对比优化前后的登录界面，展示现代化设计的改进效果</p>
        </div>

        <div class="comparison">
            <!-- 优化前 -->
            <div class="demo-section">
                <div class="demo-title old-title">❌ 优化前：边框过多，样式复杂</div>
                <div class="demo-bg">
                    <div class="old-login-form">
                        <div class="old-logo">🛡️</div>
                        <div class="form-title">心洁茗茶管理系统</div>
                        <div class="form-subtitle">欢迎回来，请登录您的管理账户</div>
                        
                        <div class="input-wrapper">
                            <span class="input-icon old-input-icon">👤</span>
                            <input type="text" class="old-input" placeholder="请输入用户名" value="admin">
                        </div>
                        
                        <div class="input-wrapper">
                            <span class="input-icon old-input-icon">🔒</span>
                            <input type="password" class="old-input" placeholder="请输入密码" value="••••••">
                        </div>
                        
                        <button class="old-btn">立即登录</button>
                    </div>
                </div>
                
                <div style="margin-top: 15px; padding: 15px; background: #fef2f2; border-radius: 8px;">
                    <h4 style="color: #dc2626; margin-bottom: 8px;">存在问题：</h4>
                    <ul style="color: #7f1d1d; font-size: 14px; padding-left: 20px;">
                        <li>边框过粗（2px），视觉负担重</li>
                        <li>圆角过大（12px），不够现代</li>
                        <li>阴影过多，层次混乱</li>
                        <li>渐变背景过于复杂</li>
                        <li>动画效果过度</li>
                    </ul>
                </div>
            </div>

            <!-- 优化后 -->
            <div class="demo-section">
                <div class="demo-title new-title">✅ 优化后：简约现代，清爽干净</div>
                <div class="new-demo-bg">
                    <div class="new-login-form">
                        <div class="logo">🛡️</div>
                        <div class="form-title">心洁茗茶管理系统</div>
                        <div class="form-subtitle">欢迎回来，请登录您的管理账户</div>
                        
                        <div class="input-wrapper">
                            <span class="input-icon">👤</span>
                            <input type="text" class="new-input" placeholder="请输入用户名" value="admin">
                        </div>
                        
                        <div class="input-wrapper">
                            <span class="input-icon">🔒</span>
                            <input type="password" class="new-input" placeholder="请输入密码" value="••••••">
                        </div>
                        
                        <button class="new-btn">立即登录</button>
                    </div>
                </div>
                
                <div style="margin-top: 15px; padding: 15px; background: #f0fdf4; border-radius: 8px;">
                    <h4 style="color: #166534; margin-bottom: 8px;">优化效果：</h4>
                    <ul style="color: #14532d; font-size: 14px; padding-left: 20px;">
                        <li>边框细化（1px），更加精致</li>
                        <li>圆角适中（8px），现代简约</li>
                        <li>阴影简化，层次清晰</li>
                        <li>纯色背景，专业干净</li>
                        <li>动画微妙，体验流畅</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="improvements">
            <h3>🚀 核心改进点</h3>
            <div class="improvement-list">
                <div class="improvement-item">
                    <h4>🎯 边框优化</h4>
                    <p>将输入框边框从2px减少到1px，减少视觉负担，更符合现代设计趋势。</p>
                    <div class="before-after">
                        <span class="before">2px 粗边框</span>
                        <span>→</span>
                        <span class="after">1px 细边框</span>
                    </div>
                </div>

                <div class="improvement-item">
                    <h4>📐 圆角调整</h4>
                    <p>输入框圆角从12px调整为8px，按钮保持一致，整体更加协调。</p>
                    <div class="before-after">
                        <span class="before">12px 大圆角</span>
                        <span>→</span>
                        <span class="after">8px 适中圆角</span>
                    </div>
                </div>

                <div class="improvement-item">
                    <h4>🎨 色彩统一</h4>
                    <p>使用统一的绿色主题（#10b981），替代复杂的蓝色渐变，品牌一致性更强。</p>
                    <div class="before-after">
                        <span class="before">蓝色渐变</span>
                        <span>→</span>
                        <span class="after">绿色主题</span>
                    </div>
                </div>

                <div class="improvement-item">
                    <h4>✨ 阴影简化</h4>
                    <p>移除复杂的多层阴影，使用简单的focus状态阴影，视觉更清爽。</p>
                    <div class="before-after">
                        <span class="before">多层阴影</span>
                        <span>→</span>
                        <span class="after">简单阴影</span>
                    </div>
                </div>

                <div class="improvement-item">
                    <h4>⚡ 性能提升</h4>
                    <p>减少动画时长（0.3s→0.2s），移除不必要的变换，提升交互响应速度。</p>
                    <div class="before-after">
                        <span class="before">0.3s 动画</span>
                        <span>→</span>
                        <span class="after">0.2s 动画</span>
                    </div>
                </div>

                <div class="improvement-item">
                    <h4>🔧 代码优化</h4>
                    <p>使用CSS类替代内联样式，解决useForm警告，代码更加规范和可维护。</p>
                    <div class="before-after">
                        <span class="before">内联样式</span>
                        <span>→</span>
                        <span class="after">CSS类</span>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 30px; background: white; border-radius: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <h3 style="color: #10b981; margin-bottom: 15px;">🎉 优化总结</h3>
            <p style="color: #6b7280; font-size: 16px; line-height: 1.6; max-width: 800px; margin: 0 auto;">
                通过简化设计元素、统一色彩主题、优化交互动画，新版登录界面更加现代化、专业化，
                同时解决了技术问题，提升了用户体验和代码质量。
            </p>
            <div style="margin-top: 20px;">
                <span style="background: #dcfce7; color: #166534; padding: 8px 16px; border-radius: 20px; font-weight: 600;">
                    ✅ 现代化设计 ✅ 技术优化 ✅ 用户体验提升
                </span>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果演示
        document.querySelectorAll('.new-input').forEach(input => {
            input.addEventListener('focus', function() {
                this.style.borderColor = '#10b981';
                this.style.boxShadow = '0 0 0 3px rgba(16, 185, 129, 0.1)';
            });
            
            input.addEventListener('blur', function() {
                this.style.borderColor = '#e5e7eb';
                this.style.boxShadow = 'none';
            });
        });

        document.querySelectorAll('.old-input').forEach(input => {
            input.addEventListener('focus', function() {
                this.style.borderColor = '#4facfe';
                this.style.backgroundColor = '#ffffff';
                this.style.boxShadow = '0 4px 12px rgba(79, 172, 254, 0.15)';
            });
            
            input.addEventListener('blur', function() {
                this.style.borderColor = '#f0f2f5';
                this.style.backgroundColor = '#fafbfc';
                this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.04)';
            });
        });
    </script>
</body>
</html>
