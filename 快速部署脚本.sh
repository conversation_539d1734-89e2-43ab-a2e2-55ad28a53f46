#!/bin/bash

# ========================================
# 心洁茶叶商城快速部署脚本
# 适用于Ubuntu 20.04 LTS
# ========================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    print_message "检查系统版本..."
    if [[ ! -f /etc/os-release ]]; then
        print_error "无法检测系统版本"
        exit 1
    fi
    
    . /etc/os-release
    if [[ "$ID" != "ubuntu" ]] || [[ "$VERSION_ID" != "20.04" ]]; then
        print_warning "建议使用Ubuntu 20.04 LTS，当前系统：$PRETTY_NAME"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 更新系统
update_system() {
    print_message "更新系统包..."
    sudo apt update
    sudo apt upgrade -y
    sudo apt install -y curl wget git unzip
}

# 安装Node.js
install_nodejs() {
    print_message "安装Node.js 18.x..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    # 验证安装
    node_version=$(node --version)
    npm_version=$(npm --version)
    print_message "Node.js版本: $node_version"
    print_message "npm版本: $npm_version"
}

# 安装MySQL
install_mysql() {
    print_message "安装MySQL 8.0..."
    sudo apt install -y mysql-server
    
    # 启动MySQL服务
    sudo systemctl start mysql
    sudo systemctl enable mysql
    
    print_warning "请手动运行 'sudo mysql_secure_installation' 来配置MySQL安全设置"
}

# 安装Redis
install_redis() {
    print_message "安装Redis..."
    sudo apt install -y redis-server
    
    # 启动Redis服务
    sudo systemctl start redis-server
    sudo systemctl enable redis-server
    
    # 测试Redis连接
    if redis-cli ping | grep -q PONG; then
        print_message "Redis安装成功"
    else
        print_error "Redis安装失败"
        exit 1
    fi
}

# 安装Nginx
install_nginx() {
    print_message "安装Nginx..."
    sudo apt install -y nginx
    
    # 启动Nginx服务
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    # 测试Nginx
    if curl -s http://localhost | grep -q "Welcome to nginx"; then
        print_message "Nginx安装成功"
    else
        print_error "Nginx安装失败"
        exit 1
    fi
}

# 安装PM2
install_pm2() {
    print_message "安装PM2..."
    sudo npm install -g pm2
    
    # 验证安装
    pm2_version=$(pm2 --version)
    print_message "PM2版本: $pm2_version"
}

# 配置防火墙
configure_firewall() {
    print_message "配置防火墙..."
    sudo ufw --force reset
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    sudo ufw allow ssh
    sudo ufw allow 'Nginx Full'
    sudo ufw --force enable
    
    print_message "防火墙配置完成"
}

# 创建项目目录
create_project_directory() {
    PROJECT_PATH="/var/www/xinjie-tea"
    print_message "创建项目目录: $PROJECT_PATH"
    
    sudo mkdir -p $PROJECT_PATH
    sudo chown -R $USER:$USER $PROJECT_PATH
    
    # 创建子目录
    mkdir -p $PROJECT_PATH/mall-server
    mkdir -p $PROJECT_PATH/xinjie.mall-admin
    mkdir -p $PROJECT_PATH/logs
    mkdir -p $PROJECT_PATH/backups
    
    print_message "项目目录创建完成"
}

# 安装SSL证书工具
install_certbot() {
    print_message "安装Certbot（SSL证书工具）..."
    sudo apt install -y certbot python3-certbot-nginx
    print_message "Certbot安装完成"
    print_warning "稍后需要手动申请SSL证书"
}

# 创建数据库配置脚本
create_database_script() {
    print_message "创建数据库配置脚本..."
    
    cat > /tmp/setup_database.sql << 'EOF'
-- 创建数据库
CREATE DATABASE IF NOT EXISTS xinjie_mall CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（请修改密码）
CREATE USER IF NOT EXISTS 'xinjie_user'@'localhost' IDENTIFIED BY 'your_strong_password_here';

-- 授权
GRANT ALL PRIVILEGES ON xinjie_mall.* TO 'xinjie_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示数据库
SHOW DATABASES;
EOF

    print_message "数据库配置脚本已创建: /tmp/setup_database.sql"
    print_warning "请修改脚本中的密码，然后运行: mysql -u root -p < /tmp/setup_database.sql"
}

# 创建环境变量模板
create_env_template() {
    print_message "创建环境变量模板..."
    
    # 后端API环境变量模板
    cat > /var/www/xinjie-tea/.env.template << 'EOF'
# ========================================
# 心洁茶叶商城后端API - 生产环境配置
# ========================================

NODE_ENV=production
PORT=4000
API_PREFIX=/api

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=xinjie_mall
DB_USER=xinjie_user
DB_PASSWORD=your_database_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your_jwt_secret_key_2024_production
JWT_EXPIRES_IN=7d

# 微信小程序配置
WX_APP_ID=your_wx_app_id
WX_APP_SECRET=your_wx_app_secret

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# 日志配置
LOG_LEVEL=error
LOG_PATH=./logs
EOF

    print_message "环境变量模板已创建: /var/www/xinjie-tea/.env.template"
}

# 创建Nginx配置模板
create_nginx_template() {
    print_message "创建Nginx配置模板..."
    
    cat > /tmp/nginx_api.conf << 'EOF'
server {
    listen 80;
    server_name api.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.your-domain.com;
    
    # SSL证书路径（使用certbot申请后会自动配置）
    # ssl_certificate /etc/letsencrypt/live/api.your-domain.com/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/api.your-domain.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:4000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    location /uploads/ {
        alias /var/www/xinjie-tea/mall-server/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

    cat > /tmp/nginx_admin.conf << 'EOF'
server {
    listen 80;
    server_name admin.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name admin.your-domain.com;
    
    # SSL证书路径（使用certbot申请后会自动配置）
    # ssl_certificate /etc/letsencrypt/live/admin.your-domain.com/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/admin.your-domain.com/privkey.pem;
    
    root /var/www/xinjie-tea/xinjie.mall-admin/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:8081;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

    print_message "Nginx配置模板已创建:"
    print_message "  API服务: /tmp/nginx_api.conf"
    print_message "  管理后台: /tmp/nginx_admin.conf"
}

# 显示部署后续步骤
show_next_steps() {
    print_message "🎉 基础环境安装完成！"
    echo
    print_message "📋 接下来的步骤："
    echo "1. 配置MySQL数据库:"
    echo "   mysql -u root -p < /tmp/setup_database.sql"
    echo
    echo "2. 上传项目代码到 /var/www/xinjie-tea/"
    echo
    echo "3. 配置环境变量:"
    echo "   cp /var/www/xinjie-tea/.env.template /var/www/xinjie-tea/mall-server/.env"
    echo "   nano /var/www/xinjie-tea/mall-server/.env"
    echo
    echo "4. 安装项目依赖:"
    echo "   cd /var/www/xinjie-tea/mall-server && npm install --production"
    echo "   cd /var/www/xinjie-tea/xinjie.mall-admin && npm install && npm run build"
    echo
    echo "5. 导入数据库表结构:"
    echo "   mysql -u xinjie_user -p xinjie_mall < 数据库建表语句.sql"
    echo
    echo "6. 申请SSL证书:"
    echo "   sudo certbot --nginx -d api.your-domain.com"
    echo "   sudo certbot --nginx -d admin.your-domain.com"
    echo
    echo "7. 配置Nginx:"
    echo "   sudo cp /tmp/nginx_api.conf /etc/nginx/sites-available/api"
    echo "   sudo cp /tmp/nginx_admin.conf /etc/nginx/sites-available/admin"
    echo "   sudo ln -s /etc/nginx/sites-available/api /etc/nginx/sites-enabled/"
    echo "   sudo ln -s /etc/nginx/sites-available/admin /etc/nginx/sites-enabled/"
    echo "   sudo nginx -t && sudo systemctl reload nginx"
    echo
    echo "8. 启动服务:"
    echo "   cd /var/www/xinjie-tea/mall-server && pm2 start ecosystem.config.js"
    echo "   cd /var/www/xinjie-tea/xinjie.mall-admin && pm2 start ecosystem.admin.config.js"
    echo "   pm2 save && pm2 startup"
    echo
    print_warning "⚠️  请确保域名已正确解析到服务器IP地址"
    print_warning "⚠️  请修改所有配置文件中的域名和密码"
}

# 主函数
main() {
    print_message "🚀 开始安装心洁茶叶商城部署环境..."
    
    check_root
    check_system
    update_system
    install_nodejs
    install_mysql
    install_redis
    install_nginx
    install_pm2
    configure_firewall
    create_project_directory
    install_certbot
    create_database_script
    create_env_template
    create_nginx_template
    show_next_steps
    
    print_message "✅ 环境安装完成！"
}

# 运行主函数
main "$@"
