import React, { useState, useEffect } from 'react';
import { Layout, Dropdown, Avatar, Button, message } from 'antd';
import { UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';
import AuthService from '../../utils/auth';

const { Header } = Layout;

const Topbar = () => {
  const [userInfo, setUserInfo] = useState(null);

  useEffect(() => {
    // 获取用户信息
    const user = AuthService.getUserInfo();
    setUserInfo(user);
  }, []);

  const handleLogout = async () => {
    try {
      await AuthService.logout();
      message.success('已退出登录');
    window.location.href = '/login';
    } catch (error) {
      console.error('登出失败:', error);
      message.error('登出失败，请重试');
    }
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => {
        // 跳转到个人资料页面
        window.location.href = '/settings/profile';
      }
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      onClick: () => {
        // 跳转到系统设置页面
        window.location.href = '/settings/basic';
      }
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout
    }
  ];

  return (
    <Header
      style={{
        background: '#fff',
        padding: '0 32px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
        zIndex: 1000,
        height: '64px',
        borderBottom: '1px solid #f0f0f0'
      }}
    >
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '24px'
      }}>
        {/* Logo和标题区域 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '16px'
        }}>
          <div style={{
            width: '36px',
            height: '36px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '18px',
            boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)'
          }}>
            🍃
          </div>
          <div>
            <div style={{
              fontSize: '20px',
              fontWeight: '600',
              color: '#1a202c',
              letterSpacing: '0.3px'
            }}>
              心洁茗茶后台管理系统
            </div>
          </div>
        </div>

        {/* 版本标识 */}
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          padding: '4px 12px',
          borderRadius: '16px',
          fontSize: '12px',
          color: '#fff',
          fontWeight: '500',
          boxShadow: '0 2px 4px rgba(102, 126, 234, 0.2)'
        }}>
          v2.0.1
        </div>
      </div>

      {/* 右侧用户区域 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '20px'
      }}>
        {/* 欢迎信息 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          color: '#4a5568',
          fontSize: '14px'
        }}>
          <span style={{
            color: '#718096',
            fontWeight: '500'
          }}>
            欢迎，
          </span>
          <span style={{
            fontWeight: '600',
            color: '#2d3748'
          }}>
            {userInfo?.username || 'admin2'}
          </span>
        </div>

        {/* 用户下拉菜单 */}
        <Dropdown
          menu={{ items: userMenuItems }}
          placement="bottomRight"
          arrow
        >
          <Button
            type="text"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              background: '#f7fafc',
              border: '1px solid #e2e8f0',
              borderRadius: '20px',
              padding: '6px 16px',
              color: '#4a5568',
              height: 'auto',
              transition: 'all 0.3s ease',
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = '#edf2f7';
              e.currentTarget.style.borderColor = '#cbd5e0';
              e.currentTarget.style.transform = 'translateY(-1px)';
              e.currentTarget.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = '#f7fafc';
              e.currentTarget.style.borderColor = '#e2e8f0';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
            }}
          >
            <Avatar
              size={28}
              icon={<UserOutlined />}
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: '#fff'
              }}
            />
            <span style={{
              fontSize: '14px',
              fontWeight: '500',
              color: '#2d3748'
            }}>
              {userInfo?.username || 'admin2'}
            </span>
          </Button>
        </Dropdown>
      </div>
    </Header>
  );
};

export default Topbar;
