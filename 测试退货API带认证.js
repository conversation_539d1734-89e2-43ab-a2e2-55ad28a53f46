// 带认证的退货API测试脚本
const http = require('http');

// 模拟登录获取session
function login() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      username: 'admin',
      password: '123456'
    });

    const options = {
      hostname: 'localhost',
      port: 8081,
      path: '/api/admin/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          const cookies = res.headers['set-cookie'];
          resolve({
            status: res.statusCode,
            data: jsonData,
            cookies: cookies
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// 带cookie的API测试
function testAPIWithAuth(path, cookies, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8081,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies ? cookies.join('; ') : ''
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function runAuthenticatedTests() {
  console.log('🚀 开始带认证的退货API测试...\n');

  try {
    // 1. 尝试登录
    console.log('🔐 步骤1: 尝试登录管理后台...');
    const loginResult = await login();
    console.log(`   登录状态码: ${loginResult.status}`);
    
    let cookies = null;
    if (loginResult.status === 200 && loginResult.cookies) {
      cookies = loginResult.cookies;
      console.log('   ✅ 登录成功，获取到认证cookie');
    } else {
      console.log('   ⚠️  登录失败或无需登录，继续测试API');
    }

    // 2. 测试退货API
    console.log('\n📊 步骤2: 测试退货API...');
    
    const tests = [
      {
        name: '获取退货统计',
        path: '/api/admin/return/statistics'
      },
      {
        name: '获取退货列表',
        path: '/api/admin/return?page=1&limit=10'
      },
      {
        name: '获取退货详情',
        path: '/api/admin/return/detail/1'
      }
    ];

    let successCount = 0;
    
    for (const test of tests) {
      try {
        console.log(`\n   📋 测试: ${test.name}`);
        console.log(`   🔗 路径: ${test.path}`);
        
        const result = await testAPIWithAuth(test.path, cookies);
        console.log(`   📊 状态码: ${result.status}`);
        
        if (result.status === 200) {
          console.log('   ✅ 请求成功！');
          successCount++;
          
          if (result.data && result.data.success) {
            console.log('   📄 响应成功');
            if (result.data.data) {
              const data = result.data.data;
              if (data.list) {
                console.log(`      - 列表数量: ${data.list.length}`);
              }
              if (data.total !== undefined) {
                console.log(`      - 总数: ${data.total}`);
              }
              if (data.page !== undefined) {
                console.log(`      - 页码: ${data.page}`);
              }
            }
          }
        } else if (result.status === 401) {
          console.log('   🔐 仍需要认证');
        } else if (result.status === 500) {
          console.log('   ❌ 服务器内部错误');
          if (result.data && result.data.message) {
            console.log(`      错误信息: ${result.data.message}`);
          }
        } else {
          console.log(`   ⚠️  其他状态: ${result.status}`);
        }
        
      } catch (error) {
        console.log(`   ❌ 请求失败: ${error.message}`);
      }
    }

    console.log(`\n📊 测试总结:`);
    console.log(`   成功请求: ${successCount}/${tests.length}`);
    
    if (successCount > 0) {
      console.log('\n🎉 部分或全部API工作正常！');
    } else {
      console.log('\n⚠️  所有API都需要进一步检查');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }

  console.log('\n💡 建议:');
  console.log('1. 如果看到500错误，检查服务器日志');
  console.log('2. 如果看到401错误，尝试在浏览器中登录后再测试');
  console.log('3. 如果看到200状态，说明API修复成功');
}

runAuthenticatedTests().catch(console.error);
