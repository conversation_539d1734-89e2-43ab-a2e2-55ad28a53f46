// 前端优化增强脚本 - 退货功能
// 这个文件包含了前端优化的建议和增强功能

// 1. 错误处理优化
const ErrorHandler = {
  // 统一的错误处理函数
  handleError(error, context = '') {
    console.error(`${context} 错误:`, error);
    
    let message = '操作失败，请稍后重试';
    
    if (error.response) {
      // 服务器返回错误
      const { status, data } = error.response;
      switch (status) {
        case 400:
          message = data.message || '请求参数错误';
          break;
        case 401:
          message = '登录已过期，请重新登录';
          // 跳转到登录页
          wx.navigateTo({ url: '/pages/login/login' });
          break;
        case 403:
          message = '没有权限执行此操作';
          break;
        case 404:
          message = '请求的资源不存在';
          break;
        case 500:
          message = '服务器内部错误';
          break;
        default:
          message = data.message || '网络错误';
      }
    } else if (error.request) {
      // 网络错误
      message = '网络连接失败，请检查网络设置';
    }
    
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
    
    return message;
  },

  // 网络状态检查
  async checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          if (res.networkType === 'none') {
            wx.showToast({
              title: '网络连接不可用',
              icon: 'none'
            });
            resolve(false);
          } else {
            resolve(true);
          }
        },
        fail: () => resolve(true) // 默认认为网络可用
      });
    });
  }
};

// 2. 表单验证增强
const FormValidator = {
  // 手机号验证
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  // 退货金额验证
  validateAmount(amount, maxAmount) {
    const num = parseFloat(amount);
    return !isNaN(num) && num > 0 && num <= maxAmount;
  },

  // 退货数量验证
  validateQuantity(quantity, maxQuantity) {
    const num = parseInt(quantity);
    return !isNaN(num) && num > 0 && num <= maxQuantity;
  },

  // 图片数量验证
  validateImageCount(images, maxCount = 6) {
    return Array.isArray(images) && images.length <= maxCount;
  },

  // 文本长度验证
  validateTextLength(text, maxLength) {
    return typeof text === 'string' && text.trim().length <= maxLength;
  }
};

// 3. 用户体验增强
const UXEnhancer = {
  // 防抖函数
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // 节流函数
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  // 图片压缩
  compressImage(filePath, quality = 0.8) {
    return new Promise((resolve, reject) => {
      wx.compressImage({
        src: filePath,
        quality: quality * 100,
        success: (res) => resolve(res.tempFilePath),
        fail: reject
      });
    });
  },

  // 批量图片压缩
  async compressImages(filePaths, quality = 0.8) {
    const compressedImages = [];
    for (const filePath of filePaths) {
      try {
        const compressed = await this.compressImage(filePath, quality);
        compressedImages.push(compressed);
      } catch (error) {
        console.warn('图片压缩失败:', filePath, error);
        compressedImages.push(filePath); // 使用原图
      }
    }
    return compressedImages;
  },

  // 显示加载状态
  showLoading(title = '加载中...', mask = true) {
    wx.showLoading({ title, mask });
  },

  // 隐藏加载状态
  hideLoading() {
    wx.hideLoading();
  },

  // 显示成功提示
  showSuccess(title, duration = 2000) {
    wx.showToast({
      title,
      icon: 'success',
      duration
    });
  },

  // 显示错误提示
  showError(title, duration = 3000) {
    wx.showToast({
      title,
      icon: 'none',
      duration
    });
  }
};

// 4. 数据缓存优化
const CacheManager = {
  // 设置缓存
  setCache(key, data, expireTime = 30 * 60 * 1000) { // 默认30分钟
    const cacheData = {
      data,
      timestamp: Date.now(),
      expireTime
    };
    wx.setStorageSync(key, cacheData);
  },

  // 获取缓存
  getCache(key) {
    try {
      const cacheData = wx.getStorageSync(key);
      if (!cacheData) return null;

      const { data, timestamp, expireTime } = cacheData;
      if (Date.now() - timestamp > expireTime) {
        wx.removeStorageSync(key);
        return null;
      }

      return data;
    } catch (error) {
      console.error('获取缓存失败:', error);
      return null;
    }
  },

  // 清除缓存
  clearCache(key) {
    wx.removeStorageSync(key);
  },

  // 清除所有退货相关缓存
  clearReturnCache() {
    const keys = ['return_settings', 'return_reasons', 'user_return_list'];
    keys.forEach(key => this.clearCache(key));
  }
};

// 5. 性能优化
const PerformanceOptimizer = {
  // 图片懒加载
  lazyLoadImages() {
    // 创建 IntersectionObserver 实例
    const observer = wx.createIntersectionObserver();
    
    observer.relativeToViewport().observe('.lazy-image', (res) => {
      if (res.intersectionRatio > 0) {
        // 图片进入视口，开始加载
        const dataset = res.target.dataset;
        if (dataset.src) {
          // 更新图片src
          this.setData({
            [`images[${dataset.index}].loaded`]: true
          });
        }
      }
    });
  },

  // 列表虚拟滚动（适用于大量数据）
  virtualScroll: {
    itemHeight: 100, // 每项高度
    visibleCount: 10, // 可见项数量
    bufferCount: 5,   // 缓冲项数量

    getVisibleRange(scrollTop, containerHeight) {
      const startIndex = Math.floor(scrollTop / this.itemHeight);
      const endIndex = Math.min(
        startIndex + this.visibleCount + this.bufferCount,
        this.totalCount - 1
      );
      return { startIndex, endIndex };
    }
  }
};

// 6. 退货功能专用工具
const ReturnUtils = {
  // 格式化退货状态
  formatReturnStatus(status) {
    const statusMap = {
      0: { text: '待审核', color: '#faad14', icon: '⏳' },
      1: { text: '审核通过', color: '#1890ff', icon: '✅' },
      2: { text: '审核拒绝', color: '#f5222d', icon: '❌' },
      3: { text: '待寄回', color: '#13c2c2', icon: '📦' },
      4: { text: '已寄回', color: '#722ed1', icon: '🚚' },
      5: { text: '验收中', color: '#2f54eb', icon: '🔍' },
      6: { text: '验收通过', color: '#52c41a', icon: '✅' },
      7: { text: '验收不通过', color: '#fa8c16', icon: '❌' },
      8: { text: '退款完成', color: '#a0d911', icon: '💰' },
      9: { text: '已取消', color: '#8c8c8c', icon: '🚫' }
    };
    return statusMap[status] || { text: '未知状态', color: '#8c8c8c', icon: '❓' };
  },

  // 计算退货进度
  calculateProgress(status) {
    const progressMap = {
      0: 10,  // 待审核
      1: 25,  // 审核通过
      2: 0,   // 审核拒绝
      3: 40,  // 待寄回
      4: 60,  // 已寄回
      5: 75,  // 验收中
      6: 90,  // 验收通过
      7: 0,   // 验收不通过
      8: 100, // 退款完成
      9: 0    // 已取消
    };
    return progressMap[status] || 0;
  },

  // 生成退货时间线
  generateTimeline(statusLogs) {
    return statusLogs.map(log => ({
      status: log.status,
      statusText: log.status_text,
      time: this.formatTime(log.created_at),
      operator: log.operator_name,
      remark: log.remark,
      isActive: true
    }));
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`;
    
    return date.toLocaleDateString();
  },

  // 验证退货条件
  validateReturnCondition(order) {
    const now = new Date();
    const deliveryTime = new Date(order.delivery_time);
    const daysDiff = (now - deliveryTime) / (1000 * 60 * 60 * 24);
    
    return {
      canReturn: order.can_return && daysDiff <= 7 && order.order_status === 3,
      reason: !order.can_return ? '该商品不支持退货' :
              daysDiff > 7 ? '超过退货时限（7天）' :
              order.order_status !== 3 ? '订单状态不支持退货' : ''
    };
  }
};

// 导出所有工具
module.exports = {
  ErrorHandler,
  FormValidator,
  UXEnhancer,
  CacheManager,
  PerformanceOptimizer,
  ReturnUtils
};
