-- 退货功能数据库更新脚本
-- 执行前请备份数据库

USE xinjie_mall;

-- 1. 检查并添加订单表的退货相关字段
-- 为现有订单表添加退货相关字段
ALTER TABLE orders 
ADD COLUMN can_return TINYINT NOT NULL DEFAULT 1 COMMENT '是否可退货(0:不可退 1:可退货)' AFTER remark,
ADD COLUMN return_deadline DATETIME COMMENT '退货截止时间' AFTER can_return,
ADD COLUMN has_return TINYINT NOT NULL DEFAULT 0 COMMENT '是否有退货申请(0:无 1:有)' AFTER return_deadline;

-- 2. 创建退货申请表
CREATE TABLE IF NOT EXISTS return_requests (
  id BIGINT NOT NULL AUTO_INCREMENT COMMENT '退货申请ID',
  return_no VARCHAR(50) NOT NULL COMMENT '退货单号',
  order_id BIGINT NOT NULL COMMENT '原订单ID',
  order_no VARCHAR(50) NOT NULL COMMENT '原订单号',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  return_type TINYINT NOT NULL DEFAULT 1 COMMENT '退货类型(1:仅退款 2:退货退款 3:换货)',
  return_reason VARCHAR(200) NOT NULL COMMENT '退货原因',
  return_description TEXT COMMENT '退货详细说明',
  return_amount DECIMAL(10,2) NOT NULL COMMENT '申请退款金额',
  return_quantity INT NOT NULL DEFAULT 1 COMMENT '退货数量',
  return_images JSON COMMENT '退货凭证图片',
  contact_phone VARCHAR(20) COMMENT '联系电话',
  return_address TEXT COMMENT '退货地址',
  status TINYINT NOT NULL DEFAULT 0 COMMENT '退货状态(0:待审核 1:审核通过 2:审核拒绝 3:待寄回 4:已寄回 5:验收中 6:验收通过 7:验收不通过 8:退款完成 9:已取消)',
  admin_remark TEXT COMMENT '管理员备注',
  refuse_reason VARCHAR(500) COMMENT '拒绝原因',
  return_express_company VARCHAR(50) COMMENT '退货快递公司',
  return_express_no VARCHAR(50) COMMENT '退货快递单号',
  return_express_time DATETIME COMMENT '退货寄出时间',
  receive_time DATETIME COMMENT '商家收货时间',
  inspect_time DATETIME COMMENT '验收时间',
  refund_time DATETIME COMMENT '退款时间',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_return_no (return_no),
  KEY idx_order_id (order_id),
  KEY idx_user_id (user_id),
  KEY idx_status (status),
  KEY idx_created_at (created_at),
  FOREIGN KEY (order_id) REFERENCES orders(id),
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退货申请表';

-- 3. 创建退货商品明细表
CREATE TABLE IF NOT EXISTS return_items (
  id BIGINT NOT NULL AUTO_INCREMENT COMMENT '退货商品ID',
  return_id BIGINT NOT NULL COMMENT '退货申请ID',
  order_item_id BIGINT NOT NULL COMMENT '原订单商品ID',
  product_id BIGINT NOT NULL COMMENT '商品ID',
  product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
  product_image VARCHAR(500) COMMENT '商品图片',
  product_price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
  return_quantity INT NOT NULL COMMENT '退货数量',
  return_amount DECIMAL(10,2) NOT NULL COMMENT '退货金额',
  return_reason VARCHAR(200) COMMENT '退货原因',
  product_condition TINYINT COMMENT '商品状态(1:完好 2:轻微瑕疵 3:严重损坏)',
  inspect_result TINYINT COMMENT '验收结果(1:通过 2:不通过)',
  inspect_remark TEXT COMMENT '验收备注',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  KEY idx_return_id (return_id),
  KEY idx_product_id (product_id),
  KEY idx_order_item_id (order_item_id),
  FOREIGN KEY (return_id) REFERENCES return_requests(id),
  FOREIGN KEY (order_item_id) REFERENCES order_items(id),
  FOREIGN KEY (product_id) REFERENCES products(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退货商品明细表';

-- 4. 创建退货状态记录表
CREATE TABLE IF NOT EXISTS return_status_logs (
  id BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  return_id BIGINT NOT NULL COMMENT '退货申请ID',
  status TINYINT NOT NULL COMMENT '状态',
  status_text VARCHAR(50) NOT NULL COMMENT '状态描述',
  operator_type TINYINT NOT NULL COMMENT '操作者类型(1:用户 2:管理员 3:系统)',
  operator_id BIGINT COMMENT '操作者ID',
  operator_name VARCHAR(50) COMMENT '操作者姓名',
  remark TEXT COMMENT '备注',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (id),
  KEY idx_return_id (return_id),
  KEY idx_created_at (created_at),
  FOREIGN KEY (return_id) REFERENCES return_requests(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退货状态记录表';

-- 5. 创建退款记录表
CREATE TABLE IF NOT EXISTS refund_records (
  id BIGINT NOT NULL AUTO_INCREMENT COMMENT '退款记录ID',
  refund_no VARCHAR(50) NOT NULL COMMENT '退款单号',
  return_id BIGINT NOT NULL COMMENT '退货申请ID',
  order_id BIGINT NOT NULL COMMENT '原订单ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  refund_amount DECIMAL(10,2) NOT NULL COMMENT '退款金额',
  refund_type TINYINT NOT NULL COMMENT '退款方式(1:原路退回 2:余额退款 3:线下退款)',
  refund_status TINYINT NOT NULL DEFAULT 0 COMMENT '退款状态(0:待退款 1:退款中 2:退款成功 3:退款失败)',
  refund_channel VARCHAR(50) COMMENT '退款渠道',
  transaction_id VARCHAR(100) COMMENT '第三方交易号',
  refund_time DATETIME COMMENT '退款时间',
  success_time DATETIME COMMENT '退款成功时间',
  fail_reason VARCHAR(500) COMMENT '退款失败原因',
  operator_id BIGINT COMMENT '操作员ID',
  operator_name VARCHAR(50) COMMENT '操作员姓名',
  remark TEXT COMMENT '备注',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_refund_no (refund_no),
  KEY idx_return_id (return_id),
  KEY idx_order_id (order_id),
  KEY idx_user_id (user_id),
  KEY idx_refund_status (refund_status),
  FOREIGN KEY (return_id) REFERENCES return_requests(id),
  FOREIGN KEY (order_id) REFERENCES orders(id),
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退款记录表';

-- 6. 创建退货配置表
CREATE TABLE IF NOT EXISTS return_settings (
  id INT NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  setting_key VARCHAR(50) NOT NULL COMMENT '配置键',
  setting_value TEXT NOT NULL COMMENT '配置值',
  setting_desc VARCHAR(200) COMMENT '配置描述',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退货配置表';

-- 7. 插入默认退货配置
INSERT IGNORE INTO return_settings (setting_key, setting_value, setting_desc) VALUES
('return_deadline_days', '7', '退货申请期限（天）'),
('return_reasons', '["质量问题", "商品描述不符", "收到商品破损", "商品缺件", "不喜欢/不合适", "其他原因"]', '退货原因选项'),
('return_address', '{"name": "心洁茗茶客服", "phone": "************", "address": "福建省福州市仓山区心洁茗茶仓储中心", "zipcode": "350000"}', '退货地址信息'),
('auto_approve_amount', '100', '自动审核通过的金额上限'),
('return_shipping_fee', '10', '退货运费'),
('inspect_timeout_hours', '72', '验收超时时间（小时）');

-- 8. 更新现有订单的退货相关字段
-- 为已发货的订单设置退货截止时间
UPDATE orders 
SET 
  can_return = CASE 
    WHEN order_status IN (2, 3) AND delivery_time IS NOT NULL THEN 1 
    WHEN order_status = 4 THEN 0 
    ELSE 1 
  END,
  return_deadline = CASE 
    WHEN order_status IN (2, 3) AND delivery_time IS NOT NULL 
    THEN DATE_ADD(delivery_time, INTERVAL 7 DAY)
    ELSE NULL 
  END
WHERE order_status > 0;

-- 9. 创建视图：退货统计
CREATE OR REPLACE VIEW return_statistics AS
SELECT 
  DATE(created_at) as date,
  COUNT(*) as total_requests,
  COUNT(CASE WHEN status = 0 THEN 1 END) as pending_count,
  COUNT(CASE WHEN status = 1 THEN 1 END) as approved_count,
  COUNT(CASE WHEN status = 2 THEN 1 END) as rejected_count,
  COUNT(CASE WHEN status = 8 THEN 1 END) as completed_count,
  SUM(return_amount) as total_amount,
  SUM(CASE WHEN status = 8 THEN return_amount ELSE 0 END) as refunded_amount
FROM return_requests 
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 10. 创建存储过程：自动处理超时的退货申请
DELIMITER //
CREATE PROCEDURE ProcessTimeoutReturns()
BEGIN
  -- 自动取消超过24小时未审核的退货申请
  UPDATE return_requests 
  SET status = 9, admin_remark = '系统自动取消：超时未审核'
  WHERE status = 0 
    AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR);
    
  -- 自动确认超过72小时未验收的退货（验收通过）
  UPDATE return_requests 
  SET status = 6, inspect_time = NOW(), admin_remark = '系统自动验收通过：超时未处理'
  WHERE status = 5 
    AND receive_time < DATE_SUB(NOW(), INTERVAL 72 HOUR);
END //
DELIMITER ;

-- 执行完成提示
SELECT '退货功能数据库更新完成！' as message;
SELECT '请检查表结构是否正确创建' as reminder;

-- 检查创建的表
SHOW TABLES LIKE '%return%';
SHOW TABLES LIKE '%refund%';
