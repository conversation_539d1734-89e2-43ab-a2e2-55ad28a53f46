<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化浅绿主题预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }

        .preview-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, #10b981 0%, #059669 50%, #047857 100%);
            box-shadow: 2px 0 20px rgba(16, 185, 129, 0.15);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="modern-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1.5" fill="rgba(255,255,255,0.04)"/><circle cx="10" cy="10" r="0.8" fill="rgba(255,255,255,0.02)"/><circle cx="30" cy="30" r="0.8" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23modern-pattern)"/></svg>');
            pointer-events: none;
        }

        .brand-logo {
            padding: 24px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.15);
            margin-bottom: 20px;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            position: relative;
        }

        .brand-logo .icon {
            font-size: 28px;
            margin-bottom: 12px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        .brand-logo .title {
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .brand-logo .subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 11px;
            font-weight: 400;
            letter-spacing: 0.3px;
        }

        .menu {
            padding: 0 16px;
        }

        .menu-item {
            color: rgba(255,255,255,0.9);
            background: transparent;
            border-radius: 12px;
            margin: 6px 0;
            padding: 0 20px;
            height: 48px;
            line-height: 48px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: 1px solid transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .menu-item:hover {
            background: rgba(255, 255, 255, 0.15);
            color: #fff;
            transform: translateX(6px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .menu-item.active {
            background: rgba(255, 255, 255, 0.25);
            color: #fff;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transform: translateX(4px);
        }

        .menu-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 28px;
            background: linear-gradient(180deg, #ffffff 0%, #f0fdf4 100%);
            border-radius: 0 4px 4px 0;
            box-shadow: 0 2px 8px rgba(255, 255, 255, 0.3);
        }

        .menu-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 20px;
        }

        .topbar {
            background: white;
            padding: 16px 24px;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.08);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .topbar-logo {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .topbar-title {
            font-size: 20px;
            font-weight: 600;
            color: #1a202c;
            letter-spacing: 0.3px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stats-card {
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid rgba(16, 185, 129, 0.08);
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%);
            border-radius: 50%;
            transform: translate(30px, -30px);
            opacity: 0.1;
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 12px 35px rgba(16, 185, 129, 0.18);
            border-color: rgba(16, 185, 129, 0.2);
        }

        .stats-card:hover::before {
            transform: translate(20px, -20px) scale(1.2);
            opacity: 0.15;
        }

        .stats-title {
            color: #6b7280;
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .stats-value {
            color: #10b981;
            font-weight: 700;
            font-size: 32px;
            margin-bottom: 8px;
        }

        .stats-change {
            font-size: 12px;
            color: #059669;
        }

        .demo-note {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin: 20px;
            text-align: center;
        }

        .demo-note h3 {
            color: #047857;
            margin-bottom: 10px;
        }

        .demo-note p {
            color: #065f46;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .preview-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-note">
        <h3>🎨 现代化浅绿主题预览</h3>
        <p>这是心洁茶叶商城管理后台的新主题预览。侧边栏采用现代化的浅绿渐变，菜单项有流畅的交互动画，整体设计更加现代和专业。</p>
    </div>

    <div class="preview-container">
        <div class="sidebar">
            <div class="brand-logo">
                <div class="icon">🍃</div>
                <div class="title">心洁茗茶</div>
                <div class="subtitle">管理后台系统</div>
            </div>
            
            <div class="menu">
                <div class="menu-item active">
                    <div class="menu-icon">📊</div>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">🛍️</div>
                    <span>商品管理</span>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">📦</div>
                    <span>订单管理</span>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">👥</div>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">💰</div>
                    <span>充值管理</span>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">📈</div>
                    <span>数据统计</span>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">⚙️</div>
                    <span>系统设置</span>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="topbar">
                <div class="topbar-logo">🍃</div>
                <div class="topbar-title">心洁茗茶后台管理系统</div>
            </div>

            <div class="stats-grid">
                <div class="stats-card">
                    <div class="stats-title">今日订单</div>
                    <div class="stats-value">128</div>
                    <div class="stats-change">↗ +12.5%</div>
                </div>
                <div class="stats-card">
                    <div class="stats-title">用户总数</div>
                    <div class="stats-value">2,456</div>
                    <div class="stats-change">↗ +8.2%</div>
                </div>
                <div class="stats-card">
                    <div class="stats-title">商品数量</div>
                    <div class="stats-value">89</div>
                    <div class="stats-change">↗ +3.1%</div>
                </div>
                <div class="stats-card">
                    <div class="stats-title">今日营收</div>
                    <div class="stats-value">¥15,680</div>
                    <div class="stats-change">↗ +18.7%</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('.menu-item:not(.active)').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelector('.menu-item.active').classList.remove('active');
                this.classList.add('active');
            });
        });

        // 添加统计卡片点击效果
        document.querySelectorAll('.stats-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'translateY(-8px) scale(1.05)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
