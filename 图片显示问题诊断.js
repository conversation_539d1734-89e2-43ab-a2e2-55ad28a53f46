#!/usr/bin/env node

/**
 * 图片显示问题诊断脚本
 * 检查图片路径配置、静态文件服务、SSL证书等问题
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

console.log('🔍 心洁茶叶商城 - 图片显示问题诊断工具\n');

// 检查项目结构
function checkProjectStructure() {
  console.log('📁 检查项目结构...');
  
  const paths = [
    './mall-server/uploads',
    './mall-server/uploads/products',
    './mall-server/uploads/banners',
    './mall-server/uploads/categories',
    './xinjie.mall-admin/public/uploads',
    './xinjie-mall-miniprogram/images'
  ];
  
  paths.forEach(dirPath => {
    if (fs.existsSync(dirPath)) {
      const files = fs.readdirSync(dirPath);
      console.log(`✅ ${dirPath} - 存在 (${files.length} 个文件)`);
      
      // 显示前3个文件作为示例
      if (files.length > 0) {
        const examples = files.slice(0, 3);
        console.log(`   示例文件: ${examples.join(', ')}`);
      }
    } else {
      console.log(`❌ ${dirPath} - 不存在`);
    }
  });
  console.log('');
}

// 检查配置文件
function checkConfigFiles() {
  console.log('⚙️ 检查配置文件...');
  
  const configs = [
    {
      file: './mall-server/src/config/index.js',
      check: (content) => {
        const cdnMatch = content.match(/cdn:\s*{[\s\S]*?domain:\s*['"](.*?)['"][\s\S]*?}/);
        const uploadMatch = content.match(/upload:\s*{[\s\S]*?path:\s*['"](.*?)['"][\s\S]*?}/);
        
        return {
          cdnDomain: cdnMatch ? cdnMatch[1] : '未配置',
          uploadPath: uploadMatch ? uploadMatch[1] : '未配置'
        };
      }
    },
    {
      file: './mall-server/.env.production',
      check: (content) => {
        const lines = content.split('\n');
        const config = {};
        lines.forEach(line => {
          const [key, value] = line.split('=');
          if (key && value !== undefined) {
            config[key.trim()] = value.trim();
          }
        });
        
        return {
          cdnDomain: config.CDN_DOMAIN || '未配置',
          cdnEnabled: config.CDN_ENABLED || '未配置',
          uploadPath: config.UPLOAD_PATH || '未配置'
        };
      }
    }
  ];
  
  configs.forEach(({ file, check }) => {
    if (fs.existsSync(file)) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const result = check(content);
        console.log(`✅ ${file}:`);
        Object.entries(result).forEach(([key, value]) => {
          console.log(`   ${key}: ${value}`);
        });
      } catch (error) {
        console.log(`❌ ${file} - 读取失败: ${error.message}`);
      }
    } else {
      console.log(`❌ ${file} - 不存在`);
    }
  });
  console.log('');
}

// 检查Nginx配置
function checkNginxConfig() {
  console.log('🌐 检查Nginx配置...');
  
  const nginxFiles = [
    './nginx-production.conf',
    '/etc/nginx/sites-available/xinjie-tea',
    '/etc/nginx/sites-enabled/xinjie-tea'
  ];
  
  nginxFiles.forEach(file => {
    if (fs.existsSync(file)) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        
        // 检查静态文件配置
        const uploadsMatch = content.match(/location\s+\/uploads\/\s*{[\s\S]*?}/);
        const sslMatch = content.match(/ssl_certificate\s+([^;]+);/);
        
        console.log(`✅ ${file}:`);
        console.log(`   静态文件配置: ${uploadsMatch ? '已配置' : '未配置'}`);
        console.log(`   SSL证书配置: ${sslMatch ? sslMatch[1] : '未配置'}`);
        
        if (uploadsMatch) {
          console.log('   静态文件配置详情:');
          console.log('   ' + uploadsMatch[0].replace(/\n/g, '\n   '));
        }
      } catch (error) {
        console.log(`❌ ${file} - 读取失败: ${error.message}`);
      }
    } else {
      console.log(`❌ ${file} - 不存在`);
    }
  });
  console.log('');
}

// 测试URL访问
function testUrlAccess() {
  console.log('🔗 测试URL访问...');
  
  const testUrls = [
    'http://localhost:4000/uploads/products/test.jpg',
    'https://api.xinjie-tea.com/uploads/products/test.jpg',
    'https://api.xinjie-tea.com/health'
  ];
  
  return Promise.all(testUrls.map(url => {
    return new Promise((resolve) => {
      const client = url.startsWith('https') ? https : http;
      const request = client.get(url, (res) => {
        console.log(`✅ ${url} - 状态码: ${res.statusCode}`);
        resolve({ url, status: res.statusCode, success: true });
      });
      
      request.on('error', (error) => {
        console.log(`❌ ${url} - 错误: ${error.message}`);
        resolve({ url, error: error.message, success: false });
      });
      
      request.setTimeout(5000, () => {
        console.log(`⏰ ${url} - 超时`);
        request.destroy();
        resolve({ url, error: '超时', success: false });
      });
    });
  }));
}

// 检查SSL证书
function checkSSLCertificate() {
  console.log('🔒 检查SSL证书...');
  
  const certPaths = [
    '/etc/letsencrypt/live/api.xinjie-tea.com/fullchain.pem',
    '/etc/letsencrypt/live/admin.xinjie-tea.com/fullchain.pem'
  ];
  
  certPaths.forEach(certPath => {
    if (fs.existsSync(certPath)) {
      try {
        const stats = fs.statSync(certPath);
        const now = new Date();
        const certTime = stats.mtime;
        const daysDiff = Math.floor((now - certTime) / (1000 * 60 * 60 * 24));
        
        console.log(`✅ ${certPath}:`);
        console.log(`   修改时间: ${certTime.toISOString()}`);
        console.log(`   距今天数: ${daysDiff} 天`);
        
        if (daysDiff > 80) {
          console.log(`   ⚠️ 证书可能即将过期，建议检查自动续期`);
        }
      } catch (error) {
        console.log(`❌ ${certPath} - 读取失败: ${error.message}`);
      }
    } else {
      console.log(`❌ ${certPath} - 不存在`);
    }
  });
  console.log('');
}

// 生成修复建议
function generateFixSuggestions() {
  console.log('💡 修复建议:\n');
  
  console.log('1. 🔧 立即可以尝试的解决方案:');
  console.log('   - 检查Nginx是否正在运行: systemctl status nginx');
  console.log('   - 重启Nginx服务: systemctl restart nginx');
  console.log('   - 检查防火墙设置: ufw status');
  console.log('   - 确保uploads目录权限正确: chmod -R 755 mall-server/uploads');
  console.log('');
  
  console.log('2. 📁 静态文件配置:');
  console.log('   - 确保Nginx配置中有正确的静态文件路径');
  console.log('   - 检查uploads目录是否存在且有文件');
  console.log('   - 验证文件路径是否正确');
  console.log('');
  
  console.log('3. 🔒 SSL证书问题:');
  console.log('   - 如果是HTTPS混合内容问题，SSL证书配置后会解决');
  console.log('   - 检查证书是否正确安装: certbot certificates');
  console.log('   - 测试证书续期: certbot renew --dry-run');
  console.log('');
  
  console.log('4. 🌐 域名和DNS:');
  console.log('   - 确认域名已正确解析到服务器IP');
  console.log('   - 检查DNS解析: nslookup api.xinjie-tea.com');
  console.log('   - 测试域名访问: curl -I https://api.xinjie-tea.com');
  console.log('');
  
  console.log('5. 📱 小程序配置:');
  console.log('   - 更新小程序中的API地址为HTTPS');
  console.log('   - 在微信后台配置正确的服务器域名');
  console.log('   - 确保request合法域名包含图片域名');
  console.log('');
}

// 主函数
async function main() {
  try {
    checkProjectStructure();
    checkConfigFiles();
    checkNginxConfig();
    checkSSLCertificate();
    
    console.log('🔗 正在测试URL访问...');
    await testUrlAccess();
    console.log('');
    
    generateFixSuggestions();
    
    console.log('✅ 诊断完成！');
    console.log('\n如果问题仍然存在，请检查以上建议或联系技术支持。');
    
  } catch (error) {
    console.error('❌ 诊断过程中出现错误:', error.message);
  }
}

// 运行诊断
main();
