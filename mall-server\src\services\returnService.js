const { 
  ReturnRequest, 
  ReturnItem, 
  ReturnStatusLog, 
  RefundRecord,
  Order,
  OrderItem,
  User,
  Product
} = require('../models');
const { Op } = require('sequelize');
const moment = require('moment');

// 退货状态映射
const RETURN_STATUS = {
  PENDING: 0,        // 待审核
  APPROVED: 1,       // 审核通过
  REJECTED: 2,       // 审核拒绝
  WAIT_RETURN: 3,    // 待寄回
  RETURNED: 4,       // 已寄回
  INSPECTING: 5,     // 验收中
  INSPECT_PASS: 6,   // 验收通过
  INSPECT_FAIL: 7,   // 验收不通过
  REFUNDED: 8,       // 退款完成
  CANCELLED: 9       // 已取消
};

const RETURN_STATUS_TEXT = {
  [RETURN_STATUS.PENDING]: '待审核',
  [RETURN_STATUS.APPROVED]: '审核通过',
  [RETURN_STATUS.REJECTED]: '审核拒绝',
  [RETURN_STATUS.WAIT_RETURN]: '待寄回',
  [RETURN_STATUS.RETURNED]: '已寄回',
  [RETURN_STATUS.INSPECTING]: '验收中',
  [RETURN_STATUS.INSPECT_PASS]: '验收通过',
  [RETURN_STATUS.INSPECT_FAIL]: '验收不通过',
  [RETURN_STATUS.REFUNDED]: '退款完成',
  [RETURN_STATUS.CANCELLED]: '已取消'
};

class ReturnService {
  // 生成退货单号
  generateReturnNo() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `RT${timestamp}${random}`;
  }

  // 生成退款单号
  generateRefundNo() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `RF${timestamp}${random}`;
  }

  // 检查订单是否可以退货
  async checkOrderCanReturn(orderId, userId) {
    const order = await Order.findOne({
      where: { 
        id: orderId,
        user_id: userId
      },
      include: [
        {
          model: OrderItem,
          as: 'orderItems',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        }
      ]
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    // 检查订单状态
    if (order.order_status < 2) {
      throw new Error('订单未发货，无法申请退货');
    }

    if (order.order_status === 4) {
      throw new Error('订单已取消，无法申请退货');
    }

    // 检查是否在退货期限内
    const deliveryTime = order.delivery_time || order.updated_at;
    const returnDeadline = moment(deliveryTime).add(7, 'days');
    
    if (moment().isAfter(returnDeadline)) {
      throw new Error('已超过退货期限');
    }

    // 检查是否已有退货申请
    const existingReturn = await ReturnRequest.findOne({
      where: {
        order_id: orderId,
        status: { [Op.notIn]: [RETURN_STATUS.REJECTED, RETURN_STATUS.CANCELLED] }
      }
    });

    if (existingReturn) {
      throw new Error('该订单已有退货申请');
    }

    return order;
  }

  // 创建退货申请
  async createReturnRequest(data) {
    const { 
      orderId, 
      userId, 
      returnType, 
      returnReason, 
      returnDescription,
      returnItems,
      contactPhone,
      returnImages
    } = data;

    // 检查订单是否可以退货
    const order = await this.checkOrderCanReturn(orderId, userId);

    // 计算退货金额
    let totalReturnAmount = 0;
    const returnItemsData = [];

    for (const item of returnItems) {
      const orderItem = order.orderItems.find(oi => oi.id === item.orderItemId);
      if (!orderItem) {
        throw new Error(`订单商品不存在: ${item.orderItemId}`);
      }

      if (item.quantity > orderItem.quantity) {
        throw new Error(`退货数量不能超过购买数量`);
      }

      const returnAmount = (orderItem.price * item.quantity).toFixed(2);
      totalReturnAmount += parseFloat(returnAmount);

      returnItemsData.push({
        order_item_id: orderItem.id,
        product_id: orderItem.product_id,
        product_name: orderItem.product_name,
        product_image: orderItem.product_image,
        product_price: orderItem.price,
        return_quantity: item.quantity,
        return_amount: returnAmount,
        return_reason: item.reason || returnReason
      });
    }

    // 创建退货申请
    const returnNo = this.generateReturnNo();
    
    const returnRequest = await ReturnRequest.create({
      return_no: returnNo,
      order_id: orderId,
      order_no: order.order_no,
      user_id: userId,
      return_type: returnType,
      return_reason: returnReason,
      return_description: returnDescription,
      return_amount: totalReturnAmount,
      return_quantity: returnItems.reduce((sum, item) => sum + item.quantity, 0),
      return_images: returnImages,
      contact_phone: contactPhone,
      status: RETURN_STATUS.PENDING
    });

    // 创建退货商品明细
    for (const itemData of returnItemsData) {
      await ReturnItem.create({
        return_id: returnRequest.id,
        ...itemData
      });
    }

    // 记录状态日志
    await this.addStatusLog(returnRequest.id, RETURN_STATUS.PENDING, {
      operatorType: 1, // 用户
      operatorId: userId,
      operatorName: '用户',
      remark: '提交退货申请'
    });

    // 更新订单退货标记
    await order.update({ has_return: 1 });

    return returnRequest;
  }

  // 添加状态日志
  async addStatusLog(returnId, status, options = {}) {
    const {
      operatorType = 3, // 系统
      operatorId = null,
      operatorName = '系统',
      remark = ''
    } = options;

    return await ReturnStatusLog.create({
      return_id: returnId,
      status,
      status_text: RETURN_STATUS_TEXT[status],
      operator_type: operatorType,
      operator_id: operatorId,
      operator_name: operatorName,
      remark
    });
  }

  // 获取退货申请列表
  async getReturnRequestList(params) {
    const {
      page = 1,
      limit = 10,
      userId,
      status,
      returnNo,
      orderNo,
      startDate,
      endDate
    } = params;

    const where = {};
    
    if (userId) {
      where.user_id = userId;
    }
    
    if (status !== undefined && status !== '') {
      where.status = parseInt(status);
    }
    
    if (returnNo) {
      where.return_no = { [Op.like]: `%${returnNo}%` };
    }
    
    if (orderNo) {
      where.order_no = { [Op.like]: `%${orderNo}%` };
    }
    
    if (startDate) {
      where.created_at = { [Op.gte]: new Date(startDate) };
    }
    
    if (endDate) {
      where.created_at = { 
        ...where.created_at,
        [Op.lte]: new Date(endDate + ' 23:59:59')
      };
    }

    const offset = (page - 1) * limit;
    
    const { count, rows } = await ReturnRequest.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'nickname', 'phone']
        },
        {
          model: Order,
          as: 'order',
          attributes: ['id', 'order_no', 'total_amount', 'pay_amount']
        },
        {
          model: ReturnItem,
          as: 'returnItems',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'main_image']
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset
    });

    return {
      list: rows,
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit)
    };
  }

  // 获取退货申请详情
  async getReturnRequestDetail(returnId, userId = null) {
    const where = { id: returnId };
    if (userId) {
      where.user_id = userId;
    }

    const returnRequest = await ReturnRequest.findOne({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'nickname', 'phone']
        },
        {
          model: Order,
          as: 'order',
          attributes: ['id', 'order_no', 'total_amount', 'pay_amount', 'delivery_time']
        },
        {
          model: ReturnItem,
          as: 'returnItems',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'main_image']
            },
            {
              model: OrderItem,
              as: 'orderItem',
              attributes: ['id', 'quantity', 'price']
            }
          ]
        },
        {
          model: ReturnStatusLog,
          as: 'statusLogs',
          order: [['created_at', 'ASC']]
        },
        {
          model: RefundRecord,
          as: 'refundRecords'
        }
      ]
    });

    if (!returnRequest) {
      throw new Error('退货申请不存在');
    }

    return returnRequest;
  }
}

module.exports = new ReturnService();
