const express = require('express');
const { query } = require('../src/config/database');
const { requireAuth } = require('../middleware/auth');
const fs = require('fs-extra');
const path = require('path');
const { uploadAndSync } = require('../utils/uploadAndSync');
const { deleteAndSync } = require('../utils/deleteAndSync');

const router = express.Router();

// 获取轮播图列表
router.get('/', async (req, res) => {
  try {
    const banners = await query(
      'SELECT * FROM banners WHERE status = 1 ORDER BY sort_order ASC, created_at DESC'
    );

    res.json({
      success: true,
      data: banners
    });
  } catch (error) {
    console.error('获取轮播图列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 创建轮播图
router.post('/', requireAuth, async (req, res) => {
  try {
    const { title, image_url, link_url, sort_order, status } = req.body;

    if (!title || !image_url) {
      return res.status(400).json({
        success: false,
        message: '标题和图片不能为空'
      });
    }

    const result = await query(
      'INSERT INTO banners (title, image_url, link_url, sort_order, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())',
      [title, image_url, link_url || '', sort_order || 0, status ? 1 : 0]
    );

    res.json({
      success: true,
      message: '轮播图创建成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('创建轮播图错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 更新轮播图
router.put('/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { title, image_url, link_url, sort_order, status } = req.body;

    if (!title || !image_url) {
      return res.status(400).json({
        success: false,
        message: '标题和图片不能为空'
      });
    }

    // 检查轮播图是否存在
    const existingBanner = await query('SELECT * FROM banners WHERE id = ?', [id]);
    if (existingBanner.length === 0) {
      return res.status(404).json({
        success: false,
        message: '轮播图不存在'
      });
    }

    // 如果更新了图片，删除旧图片
    if (image_url !== existingBanner[0].image_url && existingBanner[0].image_url) {
      const deleteResult = await deleteAndSync(existingBanner[0].image_url, 'banners');
      if (!deleteResult.success) {
        console.warn('删除旧图片失败，但继续更新:', deleteResult.message);
      }
    }

    await query(
      'UPDATE banners SET title = ?, image_url = ?, link_url = ?, sort_order = ?, status = ?, updated_at = NOW() WHERE id = ?',
      [title, image_url, link_url || '', sort_order || 0, status ? 1 : 0, id]
    );

    res.json({
      success: true,
      message: '轮播图更新成功'
    });
  } catch (error) {
    console.error('更新轮播图错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 删除轮播图
router.delete('/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // 获取轮播图信息
    const banner = await query('SELECT * FROM banners WHERE id = ?', [id]);
    if (banner.length === 0) {
      return res.status(404).json({
        success: false,
        message: '轮播图不存在'
      });
    }

    // 使用统一的删除工具删除图片
    if (banner[0].image_url) {
      const deleteResult = await deleteAndSync(banner[0].image_url, 'banners');
      if (!deleteResult.success) {
        console.warn('删除图片失败，但继续删除数据库记录:', deleteResult.message);
      }
    }

    // 删除数据库记录
    await query('DELETE FROM banners WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '轮播图删除成功'
    });
  } catch (error) {
    console.error('删除轮播图错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取单个轮播图
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const banners = await query('SELECT * FROM banners WHERE id = ?', [id]);
    if (banners.length === 0) {
      return res.status(404).json({
        success: false,
        message: '轮播图不存在'
      });
    }

    res.json({
      success: true,
      data: banners[0]
    });
  } catch (error) {
    console.error('获取轮播图错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

module.exports = router;
