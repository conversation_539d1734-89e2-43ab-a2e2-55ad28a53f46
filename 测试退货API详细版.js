// 详细测试退货API的脚本
const http = require('http');

function testAPI(path, method = 'GET', headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8081,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    console.log(`🔗 请求: ${method} ${path}`);

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function runDetailedTests() {
  console.log('🚀 开始详细测试退货API...\n');

  const tests = [
    {
      name: '测试服务器连接',
      path: '/api/admin/return/statistics',
      expectStatus: [200, 401]
    },
    {
      name: '测试退货列表 - 基本请求',
      path: '/api/admin/return?page=1&limit=10',
      expectStatus: [200, 401]
    },
    {
      name: '测试退货列表 - 带状态筛选',
      path: '/api/admin/return?page=1&limit=5&status=0',
      expectStatus: [200, 401]
    },
    {
      name: '测试退货列表 - 带搜索条件',
      path: '/api/admin/return?page=1&limit=10&returnNo=RT&orderNo=ORDER',
      expectStatus: [200, 401]
    },
    {
      name: '测试退货详情',
      path: '/api/admin/return/detail/1',
      expectStatus: [200, 401, 404]
    }
  ];

  let successCount = 0;
  let totalCount = tests.length;

  for (const test of tests) {
    try {
      console.log(`📊 ${test.name}`);
      console.log(`   路径: ${test.path}`);
      
      const startTime = Date.now();
      const result = await testAPI(test.path);
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      console.log(`   状态码: ${result.status}`);
      console.log(`   响应时间: ${responseTime}ms`);
      
      if (test.expectStatus.includes(result.status)) {
        console.log(`   ✅ 状态码符合预期`);
        successCount++;
        
        if (result.status === 200 && result.data) {
          console.log(`   📄 响应数据结构:`);
          if (result.data.success !== undefined) {
            console.log(`      - success: ${result.data.success}`);
          }
          if (result.data.data) {
            const data = result.data.data;
            if (data.list) {
              console.log(`      - 列表数量: ${data.list.length}`);
            }
            if (data.total !== undefined) {
              console.log(`      - 总数: ${data.total}`);
            }
            if (data.page !== undefined) {
              console.log(`      - 页码: ${data.page}`);
            }
            if (data.limit !== undefined) {
              console.log(`      - 每页数量: ${data.limit}`);
            }
          }
        } else if (result.status === 401) {
          console.log(`   🔐 需要认证 - 这是正常的，说明API路由工作正常`);
        }
        
      } else {
        console.log(`   ❌ 状态码不符合预期，期望: ${test.expectStatus.join('或')}`);
        if (result.data) {
          console.log(`   📄 错误响应: ${JSON.stringify(result.data, null, 2).substring(0, 300)}...`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 请求失败: ${error.message}`);
      if (error.code === 'ECONNREFUSED') {
        console.log(`   💡 提示: Express服务器可能未启动`);
      }
    }
    
    console.log('─'.repeat(60));
  }

  console.log(`\n📊 测试总结:`);
  console.log(`   成功: ${successCount}/${totalCount}`);
  console.log(`   成功率: ${Math.round(successCount/totalCount*100)}%`);

  if (successCount === totalCount) {
    console.log(`\n🎉 所有测试通过！退货API工作正常`);
  } else {
    console.log(`\n⚠️  部分测试失败，需要进一步检查`);
  }

  console.log(`\n💡 下一步建议:`);
  if (successCount > 0) {
    console.log(`   1. 登录管理后台: http://localhost:8081`);
    console.log(`   2. 访问退货管理页面测试完整功能`);
    console.log(`   3. 验证数据显示和操作功能`);
  } else {
    console.log(`   1. 检查Express服务器是否正常启动`);
    console.log(`   2. 检查数据库连接是否正常`);
    console.log(`   3. 查看服务器日志排查具体错误`);
  }
}

// 运行测试
runDetailedTests().catch(error => {
  console.error('❌ 测试运行失败:', error);
  process.exit(1);
});
