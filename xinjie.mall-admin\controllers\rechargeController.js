const rechargeModel = require('../models/rechargeModel');
const balanceModel = require('../models/balanceModel');
const userModel = require('../models/userModel');

// 获取充值记录列表
exports.list = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 10,
      userId = '',
      paymentStatus = '',
      paymentMethod = '',
      startDate = '',
      endDate = ''
    } = req.query;

    const result = await rechargeModel.findAll({
      page,
      pageSize,
      userId,
      paymentStatus,
      paymentMethod,
      startDate,
      endDate
    });

    res.json({
      success: true,
      data: result,
      message: '获取充值记录列表成功'
    });
  } catch (error) {
    console.error('获取充值记录列表失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取充值记录列表失败'
    });
  }
};

// 获取充值记录详情
exports.detail = async (req, res) => {
  try {
    const { id } = req.params;
    const recharge = await rechargeModel.findById(id);

    if (!recharge) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '充值记录不存在'
      });
    }

    res.json({
      success: true,
      data: recharge,
      message: '获取充值记录详情成功'
    });
  } catch (error) {
    console.error('获取充值记录详情失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取充值记录详情失败'
    });
  }
};

// 后台充值
exports.adminRecharge = async (req, res) => {
  try {
    const {
      user_id,
      amount,
      bonus_amount = 0,
      remark = ''
    } = req.body;

    // 验证必填字段
    if (!user_id || !amount) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '用户ID和充值金额不能为空'
      });
    }

    // 验证金额
    if (parseFloat(amount) <= 0) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '充值金额必须大于0'
      });
    }

    if (parseFloat(bonus_amount) < 0) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '赠送金额不能小于0'
      });
    }

    // 验证用户是否存在
    const user = await userModel.findById(user_id);
    if (!user) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '用户不存在'
      });
    }

    console.log('准备执行后台充值，数据:', { user_id, amount, bonus_amount, remark });

    // 执行充值
    const result = await balanceModel.adminRecharge(
      user_id,
      amount,
      bonus_amount,
      remark,
      req.user?.id || null
    );

    console.log('后台充值成功，结果:', result);

    res.json({
      success: true,
      data: result,
      message: '充值成功'
    });
  } catch (error) {
    console.error('后台充值失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: error.message || '充值失败'
    });
  }
};

// 更新充值记录
exports.update = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // 检查充值记录是否存在
    const recharge = await rechargeModel.findById(id);
    if (!recharge) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '充值记录不存在'
      });
    }

    console.log('准备更新充值记录，数据:', updateData);

    await rechargeModel.update(id, updateData);

    console.log('充值记录更新成功');

    res.json({
      success: true,
      data: { id },
      message: '充值记录更新成功'
    });
  } catch (error) {
    console.error('更新充值记录失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '更新充值记录失败'
    });
  }
};

// 删除充值记录
exports.delete = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查充值记录是否存在
    const recharge = await rechargeModel.findById(id);
    if (!recharge) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '充值记录不存在'
      });
    }

    // 只能删除未支付的记录
    if (recharge.payment_status === 1) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '已支付的充值记录不能删除'
      });
    }

    console.log('准备删除充值记录，ID:', id);

    await rechargeModel.delete(id);

    console.log('充值记录删除成功');

    res.json({
      success: true,
      data: null,
      message: '充值记录删除成功'
    });
  } catch (error) {
    console.error('删除充值记录失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '删除充值记录失败'
    });
  }
};

// 更新支付状态
exports.updatePaymentStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, transaction_id } = req.body;

    // 检查充值记录是否存在
    const recharge = await rechargeModel.findById(id);
    if (!recharge) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '充值记录不存在'
      });
    }

    console.log('准备更新支付状态，数据:', { id, status, transaction_id });

    await rechargeModel.updatePaymentStatus(id, status, transaction_id);

    console.log('支付状态更新成功');

    res.json({
      success: true,
      data: { id },
      message: '支付状态更新成功'
    });
  } catch (error) {
    console.error('更新支付状态失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '更新支付状态失败'
    });
  }
};

// 获取充值统计数据
exports.statistics = async (req, res) => {
  try {
    const { startDate = '', endDate = '' } = req.query;

    const stats = await rechargeModel.getStatistics(startDate, endDate);

    res.json({
      success: true,
      data: stats,
      message: '获取充值统计数据成功'
    });
  } catch (error) {
    console.error('获取充值统计数据失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取充值统计数据失败'
    });
  }
};

// 获取余额变动记录
exports.balanceRecords = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 10,
      userId = '',
      type = '',
      source = '',
      startDate = '',
      endDate = ''
    } = req.query;

    const result = await balanceModel.getBalanceRecords({
      page,
      pageSize,
      userId,
      type,
      source,
      startDate,
      endDate
    });

    res.json({
      success: true,
      data: result,
      message: '获取余额变动记录成功'
    });
  } catch (error) {
    console.error('获取余额变动记录失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: '获取余额变动记录失败'
    });
  }
};

// 后台调整余额
exports.adjustBalance = async (req, res) => {
  try {
    const {
      user_id,
      amount,
      type, // 1:增加 2:减少
      remark = ''
    } = req.body;

    // 验证必填字段
    if (!user_id || !amount || !type) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '用户ID、金额和类型不能为空'
      });
    }

    // 验证金额
    if (parseFloat(amount) <= 0) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '金额必须大于0'
      });
    }

    // 验证类型
    if (![1, 2].includes(parseInt(type))) {
      return res.status(400).json({
        success: false,
        data: null,
        message: '类型参数错误'
      });
    }

    // 验证用户是否存在
    const user = await userModel.findById(user_id);
    if (!user) {
      return res.status(404).json({
        success: false,
        data: null,
        message: '用户不存在'
      });
    }

    console.log('准备调整用户余额，数据:', { user_id, amount, type, remark });

    // 执行余额调整
    const newBalance = await balanceModel.adminAdjustBalance(
      user_id,
      amount,
      parseInt(type),
      remark,
      req.user?.id || null
    );

    console.log('余额调整成功，新余额:', newBalance);

    res.json({
      success: true,
      data: { newBalance },
      message: '余额调整成功'
    });
  } catch (error) {
    console.error('调整余额失败:', error);
    res.status(500).json({
      success: false,
      data: null,
      message: error.message || '调整余额失败'
    });
  }
};
