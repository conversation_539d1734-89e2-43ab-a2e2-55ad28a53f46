console.log('=== 心洁茗茶后台管理系统启动 ===');
console.log('开始加载依赖模块...');

const express = require('express');
const session = require('express-session');
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const fileUpload = require('express-fileupload'); // 恢复使用，上传接口需要
require('dotenv').config();
const config = require('./src/config');
const { testConnection } = require('./src/config/database');
const { requireAuth } = require('./src/middleware/auth');

const app = express();
const PORT = process.env.PORT || config.server.port || 8081;

// 中间件配置
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", 'data:', 'blob:'],
      },
    },
  })
);
app.use(compression());
app.use(morgan('combined'));

// CORS配置 - 支持多个前端源
app.use(
  cors({
    origin: [
      'https://localhost:4443', // API服务器
      'http://localhost:8081', // React开发服务器
      'http://localhost:3000', // Express服务器
      'http://127.0.0.1:3000',
      'http://127.0.0.1:4000',
      'http://127.0.0.1:8081',
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  })
);

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
// 启用fileUpload，上传接口需要
app.use(fileUpload());

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));
// 配置uploads静态文件服务，指向mall-server的uploads目录
app.use('/uploads', express.static(path.join(__dirname, '../mall-server/uploads')));

// 设置模板引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Session配置 - 针对本地开发环境优化
app.use(
  session({
    secret: process.env.SESSION_SECRET || 'xinjie-mall-secret-key-2024',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: false, // 本地开发必须为false
      httpOnly: true,
      maxAge: 60 * 60 * 1000, // 1小时
      sameSite: 'lax', // 兼容本地跨端口访问
      path: '/',
    },
    name: 'xinjie.sid', // 自定义session cookie名称
  })
);

// 添加session调试中间件
app.use((req, res, next) => {
  console.log('Session状态:', {
    sessionID: req.sessionID,
    hasToken: !!req.session.token,
    hasAdminId: !!req.session.adminId,
  });
  next();
});

// 路由配置
const authRoutes = require('./routes/auth');
const dashboardRoutes = require('./routes/dashboardRoutes');
const productRouter = require('./routes/product');
const categoryRoutes = require('./routes/category');
const orderRoutes = require('./routes/order');
const userRoutes = require('./routes/user');
const bannerRoutes = require('./routes/banner');
const statsRoutes = require('./routes/stats');
const settingsRoutes = require('./routes/settings');
const roleRoutes = require('./routes/role');
const permissionRoutes = require('./routes/permission');
const uploadRoutes = require('./routes/upload');
const discountRoutes = require('./routes/discountRoutes');
const rechargeRoutes = require('./routes/rechargeRoutes');
const memberRoutes = require('./routes/memberRoutes');
const cartRoutes = require('./routes/cartRoutes');
const frontOrderRoutes = require('./routes/frontOrderRoutes');
const frontApiRoutes = require('./routes/frontApiRoutes');
const systemRoutes = require('./routes/systemRoutes');
const distributionRoutes = require('./routes/distributionRoutes');
const frontDistributionRoutes = require('./routes/frontDistributionRoutes');
const returnRequestRoutes = require('./routes/returnRequest');

// API路由
app.use('/api/admin', authRoutes);
app.use('/api/admin/dashboard', dashboardRoutes);
app.use('/api/admin/product', productRouter);
app.use('/api/admin/category', categoryRoutes);
app.use('/api/admin/order', orderRoutes);
app.use('/api/admin/user', userRoutes);
app.use('/api/admin/banner', bannerRoutes);
app.use('/api/admin/stats', statsRoutes);
app.use('/api/admin/settings', settingsRoutes);
app.use('/api/admin/role', roleRoutes);
app.use('/api/admin/permission', permissionRoutes);
app.use('/api/admin/upload', uploadRoutes);
app.use('/api/admin/discount', discountRoutes);
app.use('/api/admin/recharge', rechargeRoutes);
app.use('/api/admin/member', memberRoutes);
app.use('/api/admin/system', systemRoutes);
app.use('/api/admin/distribution', distributionRoutes);
app.use('/api/admin/return', returnRequestRoutes);
app.use('/api/cart', cartRoutes);
app.use('/api/order', frontOrderRoutes);

// 前端API路由 - 余额、支付、会员功能
app.use('/api/front', frontApiRoutes);

// 前端分销API路由
app.use('/api/front/distribution', frontDistributionRoutes);

// 兼容旧版API路由 - 为了支持传统的前端调用
app.use('/api/products', productRouter);
app.use('/api/orders', orderRoutes);
app.use('/api/banners', bannerRoutes);

// 数据库测试API
app.get('/api/test-db', async (req, res) => {
  try {
    const { query } = require('./src/config/database');
    const result = await query('SELECT COUNT(*) as count FROM products');
    res.json({
      success: true,
      message: '数据库连接正常',
      data: {
        productCount: result[0].count,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('数据库测试失败:', error);
    res.status(500).json({
      success: false,
      message: '数据库连接失败: ' + error.message,
      error: error.toString()
    });
  }
});

// 页面路由
app.get('/', (req, res) => {
  res.render('index');
});

app.get('/login', (req, res) => {
  res.render('login');
});

app.get('/dashboard', (req, res) => {
  if (!req.session.token) {
    return res.redirect('/login');
  }
  res.render('dashboard');
});

app.get('/banners', requireAuth, (req, res) => {
  res.send('<h2>轮播图管理页面（开发中）</h2>');
});

app.get('/products', requireAuth, (req, res) => {
  res.send('<h2>商品管理页面（开发中）</h2>');
});

app.get('/orders', requireAuth, (req, res) => {
  res.send('<h2>订单管理页面（开发中）</h2>');
});

app.get('/users', requireAuth, (req, res) => {
  res.send('<h2>用户管理页面（开发中）</h2>');
});

app.get('/manage/banners', requireAuth, (req, res) =>
  res.render('bannerManage')
);
app.get('/manage/products', requireAuth, (req, res) =>
  res.render('productManage')
);
app.get('/manage/discounts', requireAuth, (req, res) =>
  res.render('discountManage')
);

// 添加折扣API测试页面
app.get('/test-discount-api', (req, res) => {
  res.sendFile(path.join(__dirname, 'test-discount-api.html'));
});
app.get('/manage/orders', requireAuth, (req, res) => res.render('orderManage'));
app.get('/manage/users', requireAuth, (req, res) => res.render('userManage'));
app.get('/manage/categories', requireAuth, (req, res) =>
  res.render('categoryManage')
);
app.get('/manage/settings', requireAuth, (req, res) =>
  res.render('settingsManage')
);

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.message : {},
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
  });
});

console.log('当前环境:', process.env.NODE_ENV || 'development');
console.log('服务端口:', config.server.port);
console.log('数据库:', config.database.name);

// 启动服务器
app.listen(PORT, () => {
  console.log(`心洁茶叶商城后台管理系统运行在端口 ${PORT}`);
  console.log(`访问地址: http://localhost:${PORT}`);
});

testConnection();

module.exports = app;
