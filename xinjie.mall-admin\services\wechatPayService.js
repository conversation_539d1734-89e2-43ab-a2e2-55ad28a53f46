/**
 * 微信支付服务
 * 用于接入真实的微信支付功能
 */

const crypto = require('crypto');
const axios = require('axios');

class WechatPayService {
  constructor() {
    // 微信支付配置（需要在生产环境中配置真实参数）
    this.config = {
      appid: process.env.WECHAT_APPID || 'wx8792033d9e7052f1', // 你的小程序AppID
      mchid: process.env.WECHAT_MCHID || '你的商户号',
      private_key: process.env.WECHAT_PRIVATE_KEY || '商户私钥',
      serial_no: process.env.WECHAT_SERIAL_NO || '证书序列号',
      apiv3_key: process.env.WECHAT_APIV3_KEY || 'APIv3密钥',
      notify_url: process.env.WECHAT_NOTIFY_URL || 'https://你的域名/api/payment/wechat/notify'
    };
    
    this.baseURL = 'https://api.mch.weixin.qq.com';
  }

  /**
   * 创建微信支付订单
   * @param {Object} params - 支付参数
   * @returns {Object} 支付参数
   */
  async createPayOrder(params) {
    const { userId, amount, orderNo, description = '心洁茶叶-余额充值' } = params;
    
    try {
      // 获取用户openid
      const openid = await this.getUserOpenid(userId);
      
      const paymentParams = {
        appid: this.config.appid,
        mchid: this.config.mchid,
        description,
        out_trade_no: orderNo,
        notify_url: this.config.notify_url,
        amount: {
          total: Math.round(parseFloat(amount) * 100), // 转换为分
          currency: 'CNY'
        },
        payer: {
          openid: openid
        }
      };

      // 调用微信支付API
      const response = await this.request('/v3/pay/transactions/jsapi', 'POST', paymentParams);
      
      if (response.prepay_id) {
        // 生成小程序支付参数
        return this.generateMiniProgramPayParams(response.prepay_id);
      } else {
        throw new Error('创建支付订单失败');
      }
      
    } catch (error) {
      console.error('微信支付创建订单失败:', error);
      throw new Error(`微信支付失败: ${error.message}`);
    }
  }

  /**
   * 生成小程序支付参数
   * @param {string} prepayId - 预支付ID
   * @returns {Object} 小程序支付参数
   */
  generateMiniProgramPayParams(prepayId) {
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const nonceStr = this.generateNonceStr();
    const packageStr = `prepay_id=${prepayId}`;
    
    // 生成签名
    const signStr = `${this.config.appid}\n${timestamp}\n${nonceStr}\n${packageStr}\n`;
    const paySign = this.sign(signStr);
    
    return {
      timeStamp: timestamp,
      nonceStr: nonceStr,
      package: packageStr,
      signType: 'RSA',
      paySign: paySign
    };
  }

  /**
   * 处理支付回调
   * @param {Object} callbackData - 回调数据
   * @returns {Object} 处理结果
   */
  async handlePaymentCallback(callbackData) {
    try {
      // 验证签名
      const isValid = this.verifyCallback(callbackData);
      if (!isValid) {
        throw new Error('签名验证失败');
      }

      const { resource } = callbackData;
      const decryptedData = this.decryptResource(resource);
      
      const { out_trade_no, transaction_id, trade_state } = decryptedData;
      
      if (trade_state === 'SUCCESS') {
        // 支付成功，更新订单状态
        const balanceModel = require('../models/balanceModel');
        await balanceModel.confirmRechargePayment(out_trade_no, transaction_id);
        
        // 检查会员升级
        const memberService = require('./memberService');
        const userId = await this.getRechargeUserId(out_trade_no);
        await memberService.checkAndUpgradeMember(userId);
        
        return { success: true, message: '支付成功' };
      } else {
        return { success: false, message: '支付失败' };
      }
      
    } catch (error) {
      console.error('处理支付回调失败:', error);
      throw error;
    }
  }

  /**
   * 查询支付订单状态
   * @param {string} orderNo - 订单号
   * @returns {Object} 订单状态
   */
  async queryPayOrder(orderNo) {
    try {
      const url = `/v3/pay/transactions/out-trade-no/${orderNo}?mchid=${this.config.mchid}`;
      const response = await this.request(url, 'GET');
      
      return {
        orderNo: response.out_trade_no,
        transactionId: response.transaction_id,
        tradeState: response.trade_state,
        amount: response.amount.total / 100, // 转换为元
        successTime: response.success_time
      };
      
    } catch (error) {
      console.error('查询支付订单失败:', error);
      throw error;
    }
  }

  /**
   * 发起HTTP请求
   * @param {string} url - 请求URL
   * @param {string} method - 请求方法
   * @param {Object} data - 请求数据
   * @returns {Object} 响应数据
   */
  async request(url, method, data = null) {
    const timestamp = Math.floor(Date.now() / 1000);
    const nonceStr = this.generateNonceStr();
    const body = data ? JSON.stringify(data) : '';
    
    // 生成签名
    const signStr = `${method}\n${url}\n${timestamp}\n${nonceStr}\n${body}\n`;
    const signature = this.sign(signStr);
    
    const authorization = `WECHATPAY2-SHA256-RSA2048 mchid="${this.config.mchid}",nonce_str="${nonceStr}",signature="${signature}",timestamp="${timestamp}",serial_no="${this.config.serial_no}"`;
    
    const config = {
      method,
      url: this.baseURL + url,
      headers: {
        'Authorization': authorization,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'XinjieTeaMall/1.0'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return response.data;
  }

  /**
   * 生成随机字符串
   * @param {number} length - 长度
   * @returns {string} 随机字符串
   */
  generateNonceStr(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * RSA签名
   * @param {string} data - 待签名数据
   * @returns {string} 签名
   */
  sign(data) {
    return crypto.sign('RSA-SHA256', Buffer.from(data), this.config.private_key).toString('base64');
  }

  /**
   * 验证回调签名
   * @param {Object} callbackData - 回调数据
   * @returns {boolean} 验证结果
   */
  verifyCallback(callbackData) {
    // 实际项目中需要验证微信支付的回调签名
    // 这里简化处理
    return true;
  }

  /**
   * 解密回调资源
   * @param {Object} resource - 加密资源
   * @returns {Object} 解密后的数据
   */
  decryptResource(resource) {
    // 实际项目中需要使用AES-256-GCM解密
    // 这里简化处理，直接返回模拟数据
    return {
      out_trade_no: resource.out_trade_no || 'mock_order_no',
      transaction_id: resource.transaction_id || 'mock_transaction_id',
      trade_state: 'SUCCESS'
    };
  }

  /**
   * 获取用户openid
   * @param {number} userId - 用户ID
   * @returns {string} openid
   */
  async getUserOpenid(userId) {
    // 从数据库获取用户的openid
    const db = require('../src/config/database');
    const [result] = await db.query('SELECT openid FROM users WHERE id = ?', [userId]);
    
    if (result.length > 0 && result[0].openid) {
      return result[0].openid;
    }
    
    throw new Error('用户openid不存在，请重新登录');
  }

  /**
   * 根据订单号获取用户ID
   * @param {string} orderNo - 订单号
   * @returns {number} 用户ID
   */
  async getRechargeUserId(orderNo) {
    const db = require('../src/config/database');
    const [result] = await db.query('SELECT user_id FROM recharge_records WHERE order_no = ?', [orderNo]);
    
    if (result.length > 0) {
      return result[0].user_id;
    }
    
    throw new Error('充值记录不存在');
  }
}

module.exports = new WechatPayService();
