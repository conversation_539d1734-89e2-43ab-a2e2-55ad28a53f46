#!/bin/bash

# 心洁茶叶商城图片显示问题修复脚本
# 使用方法: sudo ./修复图片显示问题.sh

set -e

echo "🔧 心洁茶叶商城 - 图片显示问题修复脚本"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行，请使用 sudo ./修复图片显示问题.sh"
        exit 1
    fi
}

# 1. 检查和创建uploads目录
fix_uploads_directory() {
    log_info "检查和修复uploads目录..."
    
    UPLOAD_DIRS=(
        "/var/www/xinjie-tea/mall-server/uploads"
        "/var/www/xinjie-tea/mall-server/uploads/products"
        "/var/www/xinjie-tea/mall-server/uploads/banners"
        "/var/www/xinjie-tea/mall-server/uploads/categories"
        "/var/www/xinjie-tea/mall-server/uploads/avatars"
        "/var/www/xinjie-tea/xinjie.mall-admin/public/uploads"
    )
    
    for dir in "${UPLOAD_DIRS[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_warn "创建目录: $dir"
            mkdir -p "$dir"
        fi
        
        # 设置正确的权限
        chown -R www-data:www-data "$dir"
        chmod -R 755 "$dir"
        log_info "已设置目录权限: $dir"
    done
}

# 2. 修复Nginx静态文件配置
fix_nginx_config() {
    log_info "检查Nginx静态文件配置..."
    
    NGINX_CONFIG="/etc/nginx/sites-available/xinjie-tea"
    
    if [[ ! -f "$NGINX_CONFIG" ]]; then
        log_error "Nginx配置文件不存在: $NGINX_CONFIG"
        return 1
    fi
    
    # 检查是否有静态文件配置
    if ! grep -q "location /uploads/" "$NGINX_CONFIG"; then
        log_warn "添加静态文件配置到Nginx"
        
        # 备份原配置
        cp "$NGINX_CONFIG" "$NGINX_CONFIG.backup.$(date +%Y%m%d_%H%M%S)"
        
        # 在server块中添加静态文件配置
        sed -i '/location \/api\//i\
    # 静态文件服务\
    location /uploads/ {\
        alias /var/www/xinjie-tea/mall-server/uploads/;\
        expires 1y;\
        add_header Cache-Control "public, immutable";\
        add_header Access-Control-Allow-Origin "*";\
        try_files $uri $uri/ =404;\
    }\
' "$NGINX_CONFIG"
        
        log_info "已添加静态文件配置"
    else
        log_info "静态文件配置已存在"
    fi
    
    # 测试Nginx配置
    if nginx -t; then
        log_info "Nginx配置测试通过"
        systemctl reload nginx
        log_info "已重新加载Nginx配置"
    else
        log_error "Nginx配置测试失败"
        return 1
    fi
}

# 3. 检查和修复SSL证书
fix_ssl_certificate() {
    log_info "检查SSL证书..."
    
    DOMAINS=("api.xinjie-tea.com" "admin.xinjie-tea.com")
    
    for domain in "${DOMAINS[@]}"; do
        CERT_PATH="/etc/letsencrypt/live/$domain/fullchain.pem"
        
        if [[ -f "$CERT_PATH" ]]; then
            # 检查证书有效期
            EXPIRE_DATE=$(openssl x509 -enddate -noout -in "$CERT_PATH" | cut -d= -f2)
            EXPIRE_TIMESTAMP=$(date -d "$EXPIRE_DATE" +%s)
            CURRENT_TIMESTAMP=$(date +%s)
            DAYS_LEFT=$(( (EXPIRE_TIMESTAMP - CURRENT_TIMESTAMP) / 86400 ))
            
            log_info "$domain 证书还有 $DAYS_LEFT 天过期"
            
            if [[ $DAYS_LEFT -lt 30 ]]; then
                log_warn "$domain 证书即将过期，尝试续期..."
                certbot renew --cert-name "$domain" --quiet
            fi
        else
            log_warn "$domain 证书不存在，尝试申请..."
            certbot --nginx -d "$domain" --non-interactive --agree-tos --email <EMAIL>
        fi
    done
    
    # 设置自动续期
    if ! crontab -l | grep -q "certbot renew"; then
        (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
        log_info "已设置SSL证书自动续期"
    fi
}

# 4. 修复文件权限
fix_file_permissions() {
    log_info "修复文件权限..."
    
    PROJECT_DIR="/var/www/xinjie-tea"
    
    if [[ -d "$PROJECT_DIR" ]]; then
        # 设置项目目录权限
        chown -R www-data:www-data "$PROJECT_DIR"
        
        # 设置目录权限
        find "$PROJECT_DIR" -type d -exec chmod 755 {} \;
        
        # 设置文件权限
        find "$PROJECT_DIR" -type f -exec chmod 644 {} \;
        
        # 设置可执行文件权限
        find "$PROJECT_DIR" -name "*.sh" -exec chmod 755 {} \;
        find "$PROJECT_DIR" -name "*.js" -path "*/bin/*" -exec chmod 755 {} \;
        
        log_info "文件权限修复完成"
    else
        log_error "项目目录不存在: $PROJECT_DIR"
    fi
}

# 5. 检查防火墙设置
fix_firewall() {
    log_info "检查防火墙设置..."
    
    if command -v ufw >/dev/null 2>&1; then
        # 确保必要端口开放
        ufw allow 80/tcp
        ufw allow 443/tcp
        ufw allow 4000/tcp
        ufw allow 8081/tcp
        
        log_info "防火墙端口配置完成"
        ufw status
    else
        log_warn "UFW防火墙未安装"
    fi
}

# 6. 重启相关服务
restart_services() {
    log_info "重启相关服务..."
    
    # 重启Nginx
    if systemctl is-active --quiet nginx; then
        systemctl restart nginx
        log_info "Nginx服务已重启"
    else
        log_error "Nginx服务未运行"
    fi
    
    # 重启Node.js应用
    if command -v pm2 >/dev/null 2>&1; then
        pm2 restart all
        log_info "PM2应用已重启"
    else
        log_warn "PM2未安装或未运行"
    fi
}

# 7. 测试图片访问
test_image_access() {
    log_info "测试图片访问..."
    
    # 创建测试图片
    TEST_IMAGE="/var/www/xinjie-tea/mall-server/uploads/test.jpg"
    if [[ ! -f "$TEST_IMAGE" ]]; then
        # 创建一个简单的测试图片
        convert -size 100x100 xc:red "$TEST_IMAGE" 2>/dev/null || {
            echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > "$TEST_IMAGE"
        }
        chown www-data:www-data "$TEST_IMAGE"
        chmod 644 "$TEST_IMAGE"
    fi
    
    # 测试本地访问
    if curl -f -s "http://localhost/uploads/test.jpg" >/dev/null; then
        log_info "本地图片访问测试通过"
    else
        log_error "本地图片访问测试失败"
    fi
    
    # 测试HTTPS访问
    if curl -f -s "https://api.xinjie-tea.com/uploads/test.jpg" >/dev/null; then
        log_info "HTTPS图片访问测试通过"
    else
        log_warn "HTTPS图片访问测试失败（可能需要等待DNS生效）"
    fi
}

# 8. 生成修复报告
generate_report() {
    log_info "生成修复报告..."
    
    REPORT_FILE="/var/log/xinjie-tea-image-fix-$(date +%Y%m%d_%H%M%S).log"
    
    {
        echo "心洁茶叶商城图片显示问题修复报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "1. 目录权限检查:"
        ls -la /var/www/xinjie-tea/mall-server/uploads/ || echo "uploads目录不存在"
        echo ""
        
        echo "2. Nginx配置检查:"
        nginx -t 2>&1
        echo ""
        
        echo "3. SSL证书检查:"
        certbot certificates 2>&1 || echo "Certbot未安装或无证书"
        echo ""
        
        echo "4. 服务状态检查:"
        systemctl status nginx --no-pager -l
        echo ""
        pm2 status 2>&1 || echo "PM2未运行"
        echo ""
        
        echo "5. 防火墙状态:"
        ufw status 2>&1 || echo "UFW未安装"
        echo ""
        
    } > "$REPORT_FILE"
    
    log_info "修复报告已保存到: $REPORT_FILE"
}

# 主函数
main() {
    log_info "开始修复图片显示问题..."
    
    check_root
    
    fix_uploads_directory
    fix_nginx_config
    fix_ssl_certificate
    fix_file_permissions
    fix_firewall
    restart_services
    test_image_access
    generate_report
    
    echo ""
    log_info "🎉 图片显示问题修复完成！"
    echo ""
    echo "📋 修复总结:"
    echo "1. ✅ 创建并设置了uploads目录权限"
    echo "2. ✅ 配置了Nginx静态文件服务"
    echo "3. ✅ 检查并更新了SSL证书"
    echo "4. ✅ 修复了文件权限问题"
    echo "5. ✅ 配置了防火墙端口"
    echo "6. ✅ 重启了相关服务"
    echo ""
    echo "💡 接下来的步骤:"
    echo "1. 等待DNS解析生效（如果刚配置域名）"
    echo "2. 在微信小程序后台配置服务器域名"
    echo "3. 更新小程序代码中的API地址为HTTPS"
    echo "4. 测试图片显示是否正常"
    echo ""
    echo "🔗 测试地址:"
    echo "- API健康检查: https://api.xinjie-tea.com/health"
    echo "- 图片测试: https://api.xinjie-tea.com/uploads/test.jpg"
    echo "- 管理后台: https://admin.xinjie-tea.com"
}

# 运行主函数
main "$@"
