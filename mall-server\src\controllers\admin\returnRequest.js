const returnService = require('../../services/returnService');
const { ReturnRequest, RefundRecord } = require('../../models');
const { Op } = require('sequelize');

// 退货状态映射
const RETURN_STATUS = {
  PENDING: 0,        // 待审核
  APPROVED: 1,       // 审核通过
  REJECTED: 2,       // 审核拒绝
  WAIT_RETURN: 3,    // 待寄回
  RETURNED: 4,       // 已寄回
  INSPECTING: 5,     // 验收中
  INSPECT_PASS: 6,   // 验收通过
  INSPECT_FAIL: 7,   // 验收不通过
  REFUNDED: 8,       // 退款完成
  CANCELLED: 9       // 已取消
};

class AdminReturnController {
  // 获取退货申请列表
  async getReturnRequestList(ctx) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        returnNo = '',
        orderNo = '',
        userName = '',
        startDate = '',
        endDate = ''
      } = ctx.query;

      const params = {
        page,
        limit,
        status,
        returnNo,
        orderNo,
        startDate,
        endDate
      };

      const result = await returnService.getReturnRequestList(params);

      // 获取状态统计
      const statusStats = await ReturnRequest.findAll({
        attributes: [
          'status',
          [ReturnRequest.sequelize.fn('COUNT', ReturnRequest.sequelize.col('id')), 'count'],
          [ReturnRequest.sequelize.fn('SUM', ReturnRequest.sequelize.col('return_amount')), 'total_amount']
        ],
        group: ['status'],
        raw: true
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          ...result,
          statusStats
        }
      };
    } catch (error) {
      console.error('获取退货申请列表失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取退货申请列表失败',
        error: error.message
      };
    }
  }

  // 获取退货申请详情
  async getReturnRequestDetail(ctx) {
    try {
      const { id } = ctx.params;

      const returnRequest = await returnService.getReturnRequestDetail(id);

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: returnRequest
      };
    } catch (error) {
      console.error('获取退货申请详情失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取退货申请详情失败',
        error: error.message
      };
    }
  }

  // 审核退货申请
  async approveReturnRequest(ctx) {
    try {
      const { id } = ctx.params;
      const { approved, refuseReason, adminRemark } = ctx.request.body;
      const adminId = ctx.state.admin.id;
      const adminName = ctx.state.admin.username;

      const returnRequest = await ReturnRequest.findByPk(id);
      if (!returnRequest) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '退货申请不存在'
        };
        return;
      }

      if (returnRequest.status !== RETURN_STATUS.PENDING) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '该申请已处理，无法重复操作'
        };
        return;
      }

      const newStatus = approved ? RETURN_STATUS.APPROVED : RETURN_STATUS.REJECTED;
      const updateData = {
        status: newStatus,
        admin_remark: adminRemark
      };

      if (!approved && refuseReason) {
        updateData.refuse_reason = refuseReason;
      }

      await returnRequest.update(updateData);

      // 记录状态日志
      await returnService.addStatusLog(id, newStatus, {
        operatorType: 2, // 管理员
        operatorId: adminId,
        operatorName: adminName,
        remark: approved ? '审核通过' : `审核拒绝：${refuseReason}`
      });

      ctx.body = {
        code: 200,
        message: approved ? '审核通过' : '审核拒绝',
        data: returnRequest
      };
    } catch (error) {
      console.error('审核退货申请失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '审核退货申请失败',
        error: error.message
      };
    }
  }

  // 确认收货
  async confirmReceive(ctx) {
    try {
      const { id } = ctx.params;
      const { expressCompany, expressNo, remark } = ctx.request.body;
      const adminId = ctx.state.admin.id;
      const adminName = ctx.state.admin.username;

      const returnRequest = await ReturnRequest.findByPk(id);
      if (!returnRequest) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '退货申请不存在'
        };
        return;
      }

      if (returnRequest.status !== RETURN_STATUS.RETURNED) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '当前状态不允许确认收货'
        };
        return;
      }

      await returnRequest.update({
        status: RETURN_STATUS.INSPECTING,
        receive_time: new Date(),
        admin_remark: remark
      });

      // 记录状态日志
      await returnService.addStatusLog(id, RETURN_STATUS.INSPECTING, {
        operatorType: 2, // 管理员
        operatorId: adminId,
        operatorName: adminName,
        remark: '确认收货，开始验收'
      });

      ctx.body = {
        code: 200,
        message: '确认收货成功',
        data: returnRequest
      };
    } catch (error) {
      console.error('确认收货失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '确认收货失败',
        error: error.message
      };
    }
  }

  // 验收商品
  async inspectGoods(ctx) {
    try {
      const { id } = ctx.params;
      const { inspectResult, inspectRemark, returnItems } = ctx.request.body;
      const adminId = ctx.state.admin.id;
      const adminName = ctx.state.admin.username;

      const returnRequest = await ReturnRequest.findByPk(id, {
        include: [
          {
            model: require('../../models').ReturnItem,
            as: 'returnItems'
          }
        ]
      });

      if (!returnRequest) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '退货申请不存在'
        };
        return;
      }

      if (returnRequest.status !== RETURN_STATUS.INSPECTING) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '当前状态不允许验收'
        };
        return;
      }

      // 更新退货商品验收结果
      if (returnItems && returnItems.length > 0) {
        for (const item of returnItems) {
          const returnItem = returnRequest.returnItems.find(ri => ri.id === item.id);
          if (returnItem) {
            await returnItem.update({
              product_condition: item.condition,
              inspect_result: item.result,
              inspect_remark: item.remark
            });
          }
        }
      }

      const newStatus = inspectResult ? RETURN_STATUS.INSPECT_PASS : RETURN_STATUS.INSPECT_FAIL;
      
      await returnRequest.update({
        status: newStatus,
        inspect_time: new Date(),
        admin_remark: inspectRemark
      });

      // 记录状态日志
      await returnService.addStatusLog(id, newStatus, {
        operatorType: 2, // 管理员
        operatorId: adminId,
        operatorName: adminName,
        remark: inspectResult ? '验收通过' : `验收不通过：${inspectRemark}`
      });

      ctx.body = {
        code: 200,
        message: inspectResult ? '验收通过' : '验收不通过',
        data: returnRequest
      };
    } catch (error) {
      console.error('验收商品失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '验收商品失败',
        error: error.message
      };
    }
  }

  // 处理退款
  async processRefund(ctx) {
    try {
      const { id } = ctx.params;
      const { refundType, refundAmount, remark } = ctx.request.body;
      const adminId = ctx.state.admin.id;
      const adminName = ctx.state.admin.username;

      const returnRequest = await ReturnRequest.findByPk(id);
      if (!returnRequest) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '退货申请不存在'
        };
        return;
      }

      if (returnRequest.status !== RETURN_STATUS.INSPECT_PASS) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '当前状态不允许退款'
        };
        return;
      }

      // 创建退款记录
      const refundNo = returnService.generateRefundNo();
      const refundRecord = await RefundRecord.create({
        refund_no: refundNo,
        return_id: returnRequest.id,
        order_id: returnRequest.order_id,
        user_id: returnRequest.user_id,
        refund_amount: refundAmount || returnRequest.return_amount,
        refund_type: refundType || 2, // 默认余额退款
        refund_status: 2, // 退款成功（模拟）
        refund_time: new Date(),
        success_time: new Date(),
        operator_id: adminId,
        operator_name: adminName,
        remark: remark
      });

      // 更新退货申请状态
      await returnRequest.update({
        status: RETURN_STATUS.REFUNDED,
        refund_time: new Date(),
        admin_remark: remark
      });

      // 记录状态日志
      await returnService.addStatusLog(id, RETURN_STATUS.REFUNDED, {
        operatorType: 2, // 管理员
        operatorId: adminId,
        operatorName: adminName,
        remark: `退款完成，金额：${refundAmount || returnRequest.return_amount}元`
      });

      ctx.body = {
        code: 200,
        message: '退款处理成功',
        data: {
          returnRequest,
          refundRecord
        }
      };
    } catch (error) {
      console.error('处理退款失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '处理退款失败',
        error: error.message
      };
    }
  }
}

module.exports = new AdminReturnController();
