import React, { useState } from 'react';
import { Form, Input, Button, Card, message, Typography, Space, Divider, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone, SafetyCertificateOutlined } from '@ant-design/icons';
import request from '../utils/request';
import AuthService from '../utils/auth';

const { Title, Text } = Typography;

const Login = () => {
  const [loading, setLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  // 添加动画样式
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-20px) rotate(1deg); }
        66% { transform: translateY(-10px) rotate(-1deg); }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .login-card {
        animation: fadeInUp 0.8s ease-out;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const onFinish = async values => {
    setLoading(true);
    try {
      const response = await request.post('/admin/login', values);
      
      if (response && response.success) {
        // 使用AuthService保存认证信息
        AuthService.saveAuth(response.data);
        
        message.success('登录成功');
        window.location.href = '/';
      } else {
        message.error(response.message || '登录失败');
      }
    } catch (error) {
      console.error('登录错误:', error);
      
      let errorMessage = '网络错误';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      message.error(errorMessage);
    } finally {
    setLoading(false);
    }
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4facfe 100%)',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* 背景装饰元素 */}
      <div
        style={{
          position: 'absolute',
          top: '10%',
          left: '10%',
          width: '200px',
          height: '200px',
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '50%',
          animation: 'float 6s ease-in-out infinite'
        }}
      />
      <div
        style={{
          position: 'absolute',
          top: '60%',
          right: '15%',
          width: '150px',
          height: '150px',
          background: 'rgba(255, 255, 255, 0.08)',
          borderRadius: '50%',
          animation: 'float 8s ease-in-out infinite reverse'
        }}
      />
      <div
        style={{
          position: 'absolute',
          bottom: '20%',
          left: '20%',
          width: '100px',
          height: '100px',
          background: 'rgba(255, 255, 255, 0.06)',
          borderRadius: '50%',
          animation: 'float 10s ease-in-out infinite'
        }}
      />

      <Card
        className="login-card"
        style={{
          width: 450,
          borderRadius: '20px',
          boxShadow: '0 25px 50px rgba(0,0,0,0.15), 0 10px 20px rgba(0,0,0,0.1)',
          border: 'none',
          overflow: 'hidden',
          backdropFilter: 'blur(10px)',
          background: 'rgba(255, 255, 255, 0.95)'
        }}
        styles={{ body: { padding: '50px 45px 40px' } }}
        >
        {/* 头部标题区域 */}
        <div style={{ textAlign: 'center', marginBottom: '45px' }}>
          <div style={{
            width: '90px',
            height: '90px',
            background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4facfe 100%)',
            borderRadius: '24px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 24px',
            boxShadow: '0 12px 30px rgba(30, 60, 114, 0.3), 0 4px 15px rgba(30, 60, 114, 0.2)',
            position: 'relative'
          }}>
            <SafetyCertificateOutlined style={{ fontSize: '42px', color: 'white' }} />
            {/* 装饰光晕 */}
            <div style={{
              position: 'absolute',
              top: '-2px',
              left: '-2px',
              right: '-2px',
              bottom: '-2px',
              background: 'linear-gradient(135deg, #4facfe 0%, #1e3c72 100%)',
              borderRadius: '26px',
              zIndex: -1,
              opacity: 0.3
            }} />
          </div>
          <Title level={2} style={{
            margin: '0 0 12px',
            color: '#1a1a1a',
            fontWeight: '700',
            fontSize: '28px',
            letterSpacing: '0.5px'
          }}>
            心洁茗茶管理系统
          </Title>
          <Text type="secondary" style={{
            fontSize: '15px',
            color: '#666',
            lineHeight: '1.5'
          }}>
            欢迎回来，请登录您的管理账户
          </Text>
        </div>

        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
          style={{ marginTop: '25px' }}
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名!' },
              { min: 3, message: '用户名至少3个字符!' }
            ]}
            style={{ marginBottom: '24px' }}
          >
            <Input
              prefix={<UserOutlined style={{ color: '#1e3c72', fontSize: '16px' }} />}
              placeholder="请输入用户名"
              style={{
                height: '54px',
                borderRadius: '12px',
                border: '2px solid #f0f2f5',
                fontSize: '15px',
                backgroundColor: '#fafbfc',
                transition: 'all 0.3s ease',
                boxShadow: '0 2px 8px rgba(0,0,0,0.04)'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = '#4facfe';
                e.target.style.backgroundColor = '#ffffff';
                e.target.style.boxShadow = '0 4px 12px rgba(79, 172, 254, 0.15)';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#f0f2f5';
                e.target.style.backgroundColor = '#fafbfc';
                e.target.style.boxShadow = '0 2px 8px rgba(0,0,0,0.04)';
              }}
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码!' },
              { min: 6, message: '密码至少6个字符!' }
            ]}
            style={{ marginBottom: '24px' }}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: '#1e3c72', fontSize: '16px' }} />}
              placeholder="请输入密码"
              iconRender={(visible) => (visible ? <EyeTwoTone twoToneColor="#1e3c72" /> : <EyeInvisibleOutlined style={{ color: '#1e3c72' }} />)}
              style={{
                height: '54px',
                borderRadius: '12px',
                border: '2px solid #f0f2f5',
                fontSize: '15px',
                backgroundColor: '#fafbfc',
                transition: 'all 0.3s ease',
                boxShadow: '0 2px 8px rgba(0,0,0,0.04)'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = '#4facfe';
                e.target.style.backgroundColor = '#ffffff';
                e.target.style.boxShadow = '0 4px 12px rgba(79, 172, 254, 0.15)';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#f0f2f5';
                e.target.style.backgroundColor = '#fafbfc';
                e.target.style.boxShadow = '0 2px 8px rgba(0,0,0,0.04)';
              }}
            />
          </Form.Item>

          {/* 记住我和忘记密码 */}
          <Form.Item style={{ marginBottom: '32px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Checkbox
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                style={{
                  fontSize: '14px',
                  color: '#666'
                }}
              >
                <span style={{ color: '#666' }}>记住我</span>
              </Checkbox>
              <a
                href="#"
                style={{
                  fontSize: '14px',
                  color: '#1e3c72',
                  textDecoration: 'none',
                  fontWeight: '500',
                  transition: 'color 0.3s ease'
                }}
                onMouseEnter={(e) => e.target.style.color = '#4facfe'}
                onMouseLeave={(e) => e.target.style.color = '#1e3c72'}
              >
                忘记密码？
              </a>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{
                width: '100%',
                height: '54px',
                borderRadius: '12px',
                background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4facfe 100%)',
                border: 'none',
                fontSize: '16px',
                fontWeight: '600',
                boxShadow: '0 6px 20px rgba(30, 60, 114, 0.3), 0 2px 10px rgba(30, 60, 114, 0.2)',
                transition: 'all 0.3s ease',
                letterSpacing: '0.5px'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 8px 25px rgba(30, 60, 114, 0.4), 0 4px 15px rgba(30, 60, 114, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 6px 20px rgba(30, 60, 114, 0.3), 0 2px 10px rgba(30, 60, 114, 0.2)';
              }}
            >
              {loading ? '登录中...' : '立即登录'}
            </Button>
          </Form.Item>
        </Form>

        {/* 底部信息 */}
        <Divider style={{ margin: '35px 0 25px', color: '#d9d9d9' }}>
          <Text type="secondary" style={{ fontSize: '13px', color: '#999' }}>
            其他登录方式
          </Text>
        </Divider>

        <div style={{ textAlign: 'center', marginBottom: '30px' }}>
          <Space size="large">
            <Button
              shape="circle"
              size="large"
              style={{
                width: '48px',
                height: '48px',
                border: '2px solid #f0f2f5',
                color: '#52c41a',
                backgroundColor: '#f6ffed',
                transition: 'all 0.3s ease',
                boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
              }}
              icon={<span style={{ fontSize: '20px', fontWeight: 'bold' }}>微</span>}
              onMouseEnter={(e) => {
                e.target.style.borderColor = '#52c41a';
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 4px 12px rgba(82, 196, 26, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.target.style.borderColor = '#f0f2f5';
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
              }}
            />
            <Button
              shape="circle"
              size="large"
              style={{
                width: '48px',
                height: '48px',
                border: '2px solid #f0f2f5',
                color: '#1890ff',
                backgroundColor: '#f0f9ff',
                transition: 'all 0.3s ease',
                boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
              }}
              icon={<span style={{ fontSize: '20px', fontWeight: 'bold' }}>支</span>}
              onMouseEnter={(e) => {
                e.target.style.borderColor = '#1890ff';
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.target.style.borderColor = '#f0f2f5';
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
              }}
            />
            <Button
              shape="circle"
              size="large"
              style={{
                width: '48px',
                height: '48px',
                border: '2px solid #f0f2f5',
                color: '#ff4d4f',
                backgroundColor: '#fff2f0',
                transition: 'all 0.3s ease',
                boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
              }}
              icon={<span style={{ fontSize: '20px', fontWeight: 'bold' }}>钉</span>}
              onMouseEnter={(e) => {
                e.target.style.borderColor = '#ff4d4f';
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.target.style.borderColor = '#f0f2f5';
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
              }}
            />
          </Space>
        </div>

        <div style={{ textAlign: 'center' }}>
          <Text type="secondary" style={{
            fontSize: '13px',
            color: '#999',
            letterSpacing: '0.3px'
          }}>
            © 2024 心洁茗茶. All rights reserved.
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default Login;
