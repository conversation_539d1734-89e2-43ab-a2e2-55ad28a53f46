import React, { useState } from 'react';
import { Form, Input, Button, Card, message, Typography, Space, Divider, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone, SafetyCertificateOutlined } from '@ant-design/icons';
import request from '../utils/request';
import AuthService from '../utils/auth';
import '../styles/login.css';

const { Title, Text } = Typography;

const Login = () => {
  const [loading, setLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [form] = Form.useForm(); // 添加form实例

  // 添加动画样式
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-20px) rotate(1deg); }
        66% { transform: translateY(-10px) rotate(-1deg); }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .login-card {
        animation: fadeInUp 0.8s ease-out;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const onFinish = async values => {
    setLoading(true);
    try {
      const response = await request.post('/admin/login', values);
      
      if (response && response.success) {
        // 使用AuthService保存认证信息
        AuthService.saveAuth(response.data);
        
        message.success('登录成功');
        window.location.href = '/';
      } else {
        message.error(response.message || '登录失败');
      }
    } catch (error) {
      console.error('登录错误:', error);
      
      let errorMessage = '网络错误';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      message.error(errorMessage);
    } finally {
    setLoading(false);
    }
  };

  return (
    <div className="login-page">
      <Card className="login-card">
        {/* 头部标题区域 */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <div style={{
            width: '80px',
            height: '80px',
            background: '#10b981',
            borderRadius: '16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 20px',
            boxShadow: '0 8px 20px rgba(16, 185, 129, 0.2)'
          }}>
            <SafetyCertificateOutlined style={{ fontSize: '36px', color: 'white' }} />
          </div>
          <Title level={2} className="login-title">
            心洁茗茶管理系统
          </Title>
          <Text className="login-subtitle">
            欢迎回来，请登录您的管理账户
          </Text>
        </div>

        <Form
          form={form}
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
          style={{ marginTop: '25px' }}
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名!' },
              { min: 3, message: '用户名至少3个字符!' }
            ]}
            style={{ marginBottom: '24px' }}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="请输入用户名"
              className="modern-input"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码!' },
              { min: 6, message: '密码至少6个字符!' }
            ]}
            style={{ marginBottom: '24px' }}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              className="modern-input"
            />
          </Form.Item>

          {/* 记住我和忘记密码 */}
          <Form.Item style={{ marginBottom: '32px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Checkbox
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                style={{
                  fontSize: '14px',
                  color: '#666'
                }}
              >
                <span style={{ color: '#666' }}>记住我</span>
              </Checkbox>
              <a href="#" className="forgot-password-link">
                忘记密码？
              </a>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className="modern-login-btn"
            >
              {loading ? '登录中...' : '立即登录'}
            </Button>
          </Form.Item>
        </Form>

        {/* 底部信息 */}
        <Divider style={{ margin: '35px 0 25px', color: '#d9d9d9' }}>
          <Text type="secondary" style={{ fontSize: '13px', color: '#999' }}>
            其他登录方式
          </Text>
        </Divider>

        <div style={{ textAlign: 'center', marginBottom: '30px' }}>
          <Space size="large">
            <Button
              shape="circle"
              size="large"
              style={{
                width: '48px',
                height: '48px',
                border: '2px solid #f0f2f5',
                color: '#52c41a',
                backgroundColor: '#f6ffed',
                transition: 'all 0.3s ease',
                boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
              }}
              icon={<span style={{ fontSize: '20px', fontWeight: 'bold' }}>微</span>}
              onMouseEnter={(e) => {
                e.target.style.borderColor = '#52c41a';
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 4px 12px rgba(82, 196, 26, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.target.style.borderColor = '#f0f2f5';
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
              }}
            />
            <Button
              shape="circle"
              size="large"
              style={{
                width: '48px',
                height: '48px',
                border: '2px solid #f0f2f5',
                color: '#1890ff',
                backgroundColor: '#f0f9ff',
                transition: 'all 0.3s ease',
                boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
              }}
              icon={<span style={{ fontSize: '20px', fontWeight: 'bold' }}>支</span>}
              onMouseEnter={(e) => {
                e.target.style.borderColor = '#1890ff';
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.target.style.borderColor = '#f0f2f5';
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
              }}
            />
            <Button
              shape="circle"
              size="large"
              style={{
                width: '48px',
                height: '48px',
                border: '2px solid #f0f2f5',
                color: '#ff4d4f',
                backgroundColor: '#fff2f0',
                transition: 'all 0.3s ease',
                boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
              }}
              icon={<span style={{ fontSize: '20px', fontWeight: 'bold' }}>钉</span>}
              onMouseEnter={(e) => {
                e.target.style.borderColor = '#ff4d4f';
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.target.style.borderColor = '#f0f2f5';
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
              }}
            />
          </Space>
        </div>

        <div style={{ textAlign: 'center' }}>
          <Text type="secondary" style={{
            fontSize: '13px',
            color: '#999',
            letterSpacing: '0.3px'
          }}>
            © 2024 心洁茗茶. All rights reserved.
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default Login;
