/* pages/order-detail/order-detail.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding-bottom: 120rpx;
}

/* 订单状态 */
.order-status {
  background: linear-gradient(135deg, #4caf50, #45a049);
  color: white;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}

.status-content {
  display: flex;
  align-items: center;
}

.status-icon {
  font-size: 60rpx;
  margin-right: 30rpx;
}

.status-text {
  display: flex;
  flex-direction: column;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 物流信息 */
.logistics-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.logistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.logistics-title {
  display: flex;
  flex-direction: column;
}

.logistics-title .title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.logistics-no {
  font-size: 24rpx;
  color: #666;
}

.toggle-icon {
  font-size: 24rpx;
  color: #999;
}

.logistics-content {
  padding: 20rpx 30rpx;
}

.logistics-item {
  position: relative;
  padding-left: 40rpx;
  margin-bottom: 30rpx;
}

.logistics-item:last-child {
  margin-bottom: 0;
}

.logistics-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ddd;
}

.logistics-item.active::before {
  background-color: #4caf50;
}

.logistics-item::after {
  content: '';
  position: absolute;
  left: 7rpx;
  top: 24rpx;
  width: 2rpx;
  height: calc(100% + 6rpx);
  background-color: #f0f0f0;
}

.logistics-item:last-child::after {
  display: none;
}

.logistics-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.logistics-desc {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.logistics-item.active .logistics-desc {
  color: #4caf50;
  font-weight: bold;
}

/* 通用区域样式 */
.address-section,
.products-section,
.cost-section,
.order-info-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-title .title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 收货地址 */
.address-content {
  padding: 30rpx;
}

.address-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.receiver {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-right: 20rpx;
}

.phone {
  font-size: 24rpx;
  color: #666;
}

.address-detail {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 商品信息 */
.products-list {
  padding: 30rpx;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-spec {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.product-price {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price {
  font-size: 28rpx;
  color: #e91e63;
  font-weight: bold;
}

.quantity {
  font-size: 24rpx;
  color: #999;
}

.product-subtotal {
  font-size: 32rpx;
  color: #e91e63;
  font-weight: bold;
  margin-left: 20rpx;
}

/* 费用明细 */
.cost-content {
  padding: 30rpx;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.cost-item:last-child {
  border-bottom: none;
}

.cost-item.total {
  font-weight: bold;
  font-size: 32rpx;
  color: #e91e63;
}

.cost-label {
  font-size: 28rpx;
  color: #333;
}

.cost-value {
  font-size: 28rpx;
  color: #333;
}

.cost-value.discount {
  color: #4caf50;
}

.cost-value.total-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #e91e63;
}

/* 订单信息 */
.order-info-content {
  padding: 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #333;
}

.info-value {
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.copy-btn {
  font-size: 24rpx;
  color: #4caf50;
  margin-left: 20rpx;
  padding: 10rpx 20rpx;
  border: 2rpx solid #4caf50;
  border-radius: 20rpx;
  background-color: transparent;
}

.copy-btn:active {
  background-color: #4caf50;
  color: white;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20rpx;
  z-index: 1000;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  border: 2rpx solid #ddd;
  background-color: #fff;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.action-btn.contact-btn {
  border-color: #999;
  color: #999;
}

.action-btn.cancel-btn {
  border-color: #999;
  color: #999;
}

.action-btn.pay-btn {
  border-color: #4caf50;
  background-color: #4caf50;
  color: #fff;
}

.action-btn.confirm-btn {
  border-color: #4caf50;
  background-color: #4caf50;
  color: #fff;
}

.action-btn.evaluate-btn {
  border-color: #ff9800;
  background-color: #ff9800;
  color: #fff;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 无数据状态 */
.no-data {
  text-align: center;
  padding: 100rpx;
  color: #999;
  font-size: 28rpx;
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .order-status {
    padding: 30rpx 20rpx;
  }

  .status-icon {
    font-size: 50rpx;
    margin-right: 20rpx;
  }

  .status-title {
    font-size: 28rpx;
  }

  .section-title {
    padding: 20rpx;
  }

  .address-content,
  .products-list,
  .cost-content,
  .order-info-content {
    padding: 20rpx;
  }

  .product-image {
    width: 100rpx;
    height: 100rpx;
  }

  .product-name {
    font-size: 26rpx;
  }
}

/* 动画效果 */
.container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
