<!--pages/order-detail/order-detail.wxml-->
<view class="container" wx:if="{{orderDetail}}">
  <!-- 订单状态 -->
  <view class="order-status">
    <view class="status-content">
      <view class="status-icon">
        <text wx:if="{{orderDetail.order_status === 0}}">💰</text>
        <text wx:elif="{{orderDetail.order_status === 1}}">📦</text>
        <text wx:elif="{{orderDetail.order_status === 2}}">🚚</text>
        <text wx:elif="{{orderDetail.order_status === 3}}">✅</text>
        <text wx:elif="{{orderDetail.order_status === 4}}">❌</text>
        <text wx:else>❓</text>
      </view>
      <view class="status-text">
        <text class="status-title">{{orderDetail.statusText}}</text>
        <text class="status-desc" wx:if="{{orderDetail.order_status === 0}}">请及时完成支付</text>
        <text class="status-desc" wx:elif="{{orderDetail.order_status === 1}}">商家正在为您准备商品</text>
        <text class="status-desc" wx:elif="{{orderDetail.order_status === 2}}">商品正在配送途中</text>
        <text class="status-desc" wx:elif="{{orderDetail.order_status === 3}}">感谢您的购买</text>
        <text class="status-desc" wx:elif="{{orderDetail.order_status === 4}}">订单已取消</text>
      </view>
    </view>
  </view>

  <!-- 物流信息 -->
  <view class="logistics-section" wx:if="{{orderDetail.order_status === 2}}">
    <view class="logistics-header" bindtap="onToggleLogistics">
      <view class="logistics-title">
        <text class="title">物流信息</text>
        <text class="logistics-no" wx:if="{{orderDetail.delivery_no}}">{{orderDetail.delivery_no}}</text>
      </view>
      <text class="toggle-icon">{{showLogistics ? '▼' : '▶'}}</text>
    </view>
    
    <view class="logistics-content" wx:if="{{showLogistics}}">
      <view 
        class="logistics-item {{item.isActive ? 'active' : ''}}"
        wx:for="{{logistics}}" 
        wx:key="index"
      >
        <view class="logistics-time">{{item.timeText}}</view>
        <view class="logistics-desc">{{item.content}}</view>
      </view>
    </view>
  </view>

  <!-- 收货地址 -->
  <view class="address-section">
    <view class="section-title">
      <text class="title">收货地址</text>
    </view>
    <view class="address-content">
      <view class="address-info">
        <text class="receiver">{{orderDetail.receiver_name}}</text>
        <text class="phone">{{orderDetail.receiver_phone}}</text>
      </view>
      <view class="address-detail">
        <text>{{orderDetail.receiver_address}}</text>
      </view>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="products-section">
    <view class="section-title">
      <text class="title">商品信息</text>
    </view>
    <view class="products-list">
      <view 
        class="product-item"
        wx:for="{{orderDetail.items}}" 
        wx:key="id"
        bindtap="onProductDetail"
        data-product-id="{{item.product_id}}"
      >
        <image 
          class="product-image" 
          src="{{item.product_image || defaultImage}}" 
          mode="aspectFill"
        ></image>
        <view class="product-info">
          <text class="product-name">{{item.product_name}}</text>
          <text class="product-spec">{{item.specification}}</text>
          <view class="product-price">
            <text class="price">{{item.priceText}}</text>
            <text class="quantity">x{{item.quantity}}</text>
          </view>
        </view>
        <view class="product-subtotal">
          <text>{{item.subtotalText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 费用明细 -->
  <view class="cost-section">
    <view class="section-title">
      <text class="title">费用明细</text>
    </view>
    <view class="cost-content">
      <view class="cost-item">
        <text class="cost-label">商品金额</text>
        <text class="cost-value">{{orderDetail.totalPriceText}}</text>
      </view>
      <view class="cost-item">
        <text class="cost-label">运费</text>
        <text class="cost-value">{{orderDetail.freightAmountText}}</text>
      </view>
      <view class="cost-item" wx:if="{{orderDetail.discount_amount > 0}}">
        <text class="cost-label">优惠</text>
        <text class="cost-value discount">-{{orderDetail.discountAmountText}}</text>
      </view>
      <view class="cost-item total">
        <text class="cost-label">实付金额</text>
        <text class="cost-value total-price">{{orderDetail.payAmountText}}</text>
      </view>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="order-info-section">
    <view class="section-title">
      <text class="title">订单信息</text>
    </view>
    <view class="order-info-content">
      <view class="info-item">
        <text class="info-label">订单编号</text>
        <view class="info-value">
          <text>{{orderDetail.order_no}}</text>
          <text class="copy-btn" bindtap="onCopyOrderNo">复制</text>
        </view>
      </view>
      <view class="info-item">
        <text class="info-label">下单时间</text>
        <text class="info-value">{{orderDetail.createTimeText}}</text>
      </view>
      <view class="info-item" wx:if="{{orderDetail.pay_time}}">
        <text class="info-label">支付时间</text>
        <text class="info-value">{{orderDetail.payTimeText}}</text>
      </view>
      <view class="info-item" wx:if="{{orderDetail.delivery_time}}">
        <text class="info-label">发货时间</text>
        <text class="info-value">{{orderDetail.deliveryTimeText}}</text>
      </view>
      <view class="info-item" wx:if="{{orderDetail.receive_time}}">
        <text class="info-label">完成时间</text>
        <text class="info-value">{{orderDetail.receiveTimeText}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">支付方式</text>
        <text class="info-value">{{orderDetail.paymentMethodText}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="action-btn contact-btn" bindtap="onContact">联系客服</button>
    
    <!-- 待付款状态 -->
    <block wx:if="{{orderDetail.order_status === 0}}">
      <button class="action-btn cancel-btn" bindtap="onCancelOrder">取消订单</button>
      <button class="action-btn pay-btn" bindtap="onPay">立即付款</button>
    </block>

    <!-- 待收货状态 -->
    <block wx:elif="{{orderDetail.order_status === 2}}">
      <button class="action-btn confirm-btn" bindtap="onConfirmOrder">确认收货</button>
    </block>
  </view>
</view>

<!-- 加载状态 -->
<loading wx:if="{{loading}}" text="加载中..." />

<!-- 无数据状态 -->
<view class="no-data" wx:if="{{!orderDetail && !loading}}">
  <text>订单不存在或已被删除</text>
</view> 