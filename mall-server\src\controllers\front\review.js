const { Review, User, Product, Order } = require('../../models');
const { Op } = require('sequelize');

// 发表商品评价
const createReview = async (ctx) => {
  try {
    const { orderId, productId, rating, content, images, isAnonymous } = ctx.request.body;
    const userId = ctx.state.user.id;

    // 验证参数
    if (!orderId || !productId || !rating) {
      return ctx.body = {
        code: 400,
        message: '缺少必要参数'
      };
    }

    // 验证评分范围
    if (rating < 1 || rating > 5) {
      return ctx.body = {
        code: 400,
        message: '评分必须在1-5之间'
      };
    }

    // 检查用户是否已购买该商品
    const order = await Order.findOne({
      where: {
        id: orderId,
        user_id: userId,
        order_status: 3 // 已完成状态
      },
      include: [{
        model: require('../../models/orderItem')(ctx.app.context.sequelize),
        as: 'orderItems',
        where: {
          product_id: productId
        }
      }]
    });

    if (!order) {
      return ctx.body = {
        code: 400,
        message: '您还没有购买过该商品或订单未完成'
      };
    }

    // 检查是否已经评价过
    const existingReview = await Review.findOne({
      where: {
        order_id: orderId,
        product_id: productId,
        user_id: userId
      }
    });

    if (existingReview) {
      return ctx.body = {
        code: 400,
        message: '您已经评价过该商品'
      };
    }

    // 创建评价
    const review = await Review.create({
      order_id: orderId,
      product_id: productId,
      user_id: userId,
      rating,
      content,
      images: images ? JSON.stringify(images) : null,
      is_anonymous: isAnonymous ? 1 : 0
    });

    // 更新商品评分和评价数量
    const product = await Product.findByPk(productId);
    if (product) {
      const allReviews = await Review.findAll({
        where: { product_id: productId }
      });
      
      const totalRating = allReviews.reduce((sum, r) => sum + r.rating, 0);
      const avgRating = totalRating / allReviews.length;
      
      await product.update({
        rating: parseFloat(avgRating.toFixed(2)),
        review_count: allReviews.length
      });
    }

    ctx.body = {
      code: 200,
      message: '评价发表成功',
      data: review
    };
  } catch (error) {
    console.error('发表评价失败:', error);
    ctx.body = {
      code: 500,
      message: '发表评价失败'
    };
  }
};

// 获取商品评价列表
const getProductReviews = async (ctx) => {
  try {
    const { productId } = ctx.params;
    const { page = 1, limit = 10, rating } = ctx.query;

    const where = {
      product_id: productId
    };

    if (rating) {
      where.rating = rating;
    }

    const reviews = await Review.findAndCountAll({
      where,
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'nickname', 'avatar'],
        where: {
          status: 1 // 只显示正常用户的评价
        }
      }],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    });

    // 处理评价数据
    const processedReviews = reviews.rows.map(review => {
      const reviewData = review.toJSON();
      return {
        id: reviewData.id,
        rating: reviewData.rating,
        content: reviewData.content,
        images: reviewData.images ? JSON.parse(reviewData.images) : [],
        isAnonymous: reviewData.is_anonymous === 1,
        replyContent: reviewData.reply_content,
        replyTime: reviewData.reply_time,
        createTime: reviewData.created_at,
        user: reviewData.is_anonymous === 1 ? {
          id: 0,
          nickname: '匿名用户',
          avatar: '/images/common/default-avatar.png'
        } : reviewData.user
      };
    });

    ctx.body = {
      code: 200,
      message: '获取评价列表成功',
      data: {
        list: processedReviews,
        pagination: {
          total: reviews.count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(reviews.count / parseInt(limit))
        }
      }
    };
  } catch (error) {
    console.error('获取商品评价失败:', error);
    ctx.body = {
      code: 500,
      message: '获取商品评价失败'
    };
  }
};

// 获取用户评价列表
const getUserReviews = async (ctx) => {
  try {
    const userId = ctx.state.user.id;
    const { page = 1, limit = 10 } = ctx.query;

    const reviews = await Review.findAndCountAll({
      where: {
        user_id: userId
      },
      include: [{
        model: Product,
        as: 'product',
        attributes: ['id', 'name', 'main_image']
      }],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    });

    // 处理评价数据
    const processedReviews = reviews.rows.map(review => {
      const reviewData = review.toJSON();
      return {
        id: reviewData.id,
        rating: reviewData.rating,
        content: reviewData.content,
        images: reviewData.images ? JSON.parse(reviewData.images) : [],
        isAnonymous: reviewData.is_anonymous === 1,
        replyContent: reviewData.reply_content,
        replyTime: reviewData.reply_time,
        createTime: reviewData.created_at,
        product: reviewData.product
      };
    });

    ctx.body = {
      code: 200,
      message: '获取用户评价成功',
      data: {
        list: processedReviews,
        pagination: {
          total: reviews.count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(reviews.count / parseInt(limit))
        }
      }
    };
  } catch (error) {
    console.error('获取用户评价失败:', error);
    ctx.body = {
      code: 500,
      message: '获取用户评价失败'
    };
  }
};

// 删除评价
const deleteReview = async (ctx) => {
  try {
    const { id } = ctx.params;
    const userId = ctx.state.user.id;

    const review = await Review.findOne({
      where: {
        id,
        user_id: userId
      }
    });

    if (!review) {
      return ctx.body = {
        code: 404,
        message: '评价不存在或无权删除'
      };
    }

    await review.destroy();

    // 更新商品评分和评价数量
    const product = await Product.findByPk(review.product_id);
    if (product) {
      const allReviews = await Review.findAll({
        where: { product_id: review.product_id }
      });
      
      if (allReviews.length > 0) {
        const totalRating = allReviews.reduce((sum, r) => sum + r.rating, 0);
        const avgRating = totalRating / allReviews.length;
        
        await product.update({
          rating: parseFloat(avgRating.toFixed(2)),
          review_count: allReviews.length
        });
      } else {
        await product.update({
          rating: 0.00,
          review_count: 0
        });
      }
    }

    ctx.body = {
      code: 200,
      message: '评价删除成功'
    };
  } catch (error) {
    console.error('删除评价失败:', error);
    ctx.body = {
      code: 500,
      message: '删除评价失败'
    };
  }
};

// 检查用户是否可以评价商品
const checkCanReview = async (ctx) => {
  try {
    const { productId } = ctx.params;
    const userId = ctx.state.user.id;

    // 查找用户已完成的订单中包含该商品的订单
    const order = await Order.findOne({
      where: {
        user_id: userId,
        order_status: 3 // 已完成状态
      },
      include: [{
        model: require('../../models/orderItem')(ctx.app.context.sequelize),
        as: 'orderItems',
        where: {
          product_id: productId
        }
      }]
    });

    if (!order) {
      return ctx.body = {
        code: 200,
        message: '您还没有购买过该商品或订单未完成',
        data: {
          canReview: false,
          reason: '未购买或订单未完成'
        }
      };
    }

    // 检查是否已经评价过
    const existingReview = await Review.findOne({
      where: {
        order_id: order.id,
        product_id: productId,
        user_id: userId
      }
    });

    if (existingReview) {
      return ctx.body = {
        code: 200,
        message: '您已经评价过该商品',
        data: {
          canReview: false,
          reason: '已评价',
          review: existingReview
        }
      };
    }

    ctx.body = {
      code: 200,
      message: '可以评价',
      data: {
        canReview: true,
        orderId: order.id
      }
    };
  } catch (error) {
    console.error('检查评价权限失败:', error);
    ctx.body = {
      code: 500,
      message: '检查评价权限失败'
    };
  }
};

module.exports = {
  createReview,
  getProductReviews,
  getUserReviews,
  deleteReview,
  checkCanReview
}; 