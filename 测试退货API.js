// 测试退货API的简单脚本
const http = require('http');

function testAPI(path, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8081,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function runTests() {
  console.log('🚀 开始测试退货API...\n');

  const tests = [
    {
      name: '获取退货统计',
      path: '/api/admin/return/statistics'
    },
    {
      name: '获取退货列表',
      path: '/api/admin/return?page=1&limit=10'
    },
    {
      name: '获取退货详情',
      path: '/api/admin/return/detail/1'
    }
  ];

  for (const test of tests) {
    try {
      console.log(`📊 测试: ${test.name}`);
      console.log(`🔗 路径: ${test.path}`);
      
      const result = await testAPI(test.path);
      
      if (result.status === 200) {
        console.log(`✅ 成功 (状态码: ${result.status})`);
        if (result.data && typeof result.data === 'object') {
          console.log(`📄 数据: ${JSON.stringify(result.data, null, 2).substring(0, 200)}...`);
        }
      } else if (result.status === 401) {
        console.log(`🔐 需要认证 (状态码: ${result.status}) - 这是正常的`);
      } else {
        console.log(`❌ 失败 (状态码: ${result.status})`);
        console.log(`📄 响应: ${JSON.stringify(result.data, null, 2)}`);
      }
      
      console.log('─'.repeat(50));
      
    } catch (error) {
      console.log(`❌ 请求失败: ${error.message}`);
      console.log('─'.repeat(50));
    }
  }

  console.log('\n🎉 测试完成！');
  console.log('\n💡 提示:');
  console.log('- 如果看到401错误，说明需要先登录管理后台');
  console.log('- 如果看到500错误，说明服务器内部有问题');
  console.log('- 如果看到200状态码，说明API正常工作');
}

runTests().catch(console.error);
