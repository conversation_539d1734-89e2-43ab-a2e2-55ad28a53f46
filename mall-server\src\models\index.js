const { Sequelize } = require('sequelize');
const config = require('../config');

// 创建Sequelize实例，支持读写分离
const sequelize = new Sequelize(
  config.database.database,
  config.database.username,
  config.database.password,
  {
    host: config.database.host,
    port: config.database.port,
    dialect: config.database.dialect,
    timezone: config.database.timezone,
    logging: config.database.logging,
    pool: config.database.pool,
    define: {
      timestamps: true,
      underscored: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at'
    },
    // 读写分离配置
    replication: config.database.replication || null
  }
);

// 导入模型
const User = require('./user')(sequelize);
const AdminUser = require('./adminUser')(sequelize);
const Category = require('./category')(sequelize);
const Product = require('./product')(sequelize);
const CartItem = require('./cartItem')(sequelize);
const Address = require('./address')(sequelize);
const Order = require('./order')(sequelize);
const OrderItem = require('./orderItem')(sequelize);
const Review = require('./review')(sequelize);
const Banner = require('./banner')(sequelize);
const Role = require('./role')(sequelize);
const Permission = require('./permission')(sequelize);
const RolePermission = require('./rolePermission')(sequelize);
const Setting = require('./setting')(sequelize);
// 折扣相关模型
const Discount = require('./Discount')(sequelize);
const ProductDiscount = require('./ProductDiscount')(sequelize);
const CategoryDiscount = require('./CategoryDiscount')(sequelize);
const UserDiscountUsage = require('./UserDiscountUsage')(sequelize);

// 定义模型关联关系
// 用户与地址
User.hasMany(Address, { foreignKey: 'user_id', as: 'addresses' });
Address.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 用户与购物车
User.hasMany(CartItem, { foreignKey: 'user_id', as: 'cartItems' });
CartItem.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 用户与订单
User.hasMany(Order, { foreignKey: 'user_id', as: 'orders' });
Order.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 用户与评价
User.hasMany(Review, { foreignKey: 'user_id', as: 'reviews' });
Review.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 分类与商品
Category.hasMany(Product, { foreignKey: 'category_id', as: 'products' });
Product.belongsTo(Category, { foreignKey: 'category_id', as: 'category' });

// 商品与购物车
Product.hasMany(CartItem, { foreignKey: 'product_id', as: 'cartItems' });
CartItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

// 商品与评价
Product.hasMany(Review, { foreignKey: 'product_id', as: 'reviews' });
Review.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

// 订单与订单商品
Order.hasMany(OrderItem, { foreignKey: 'order_id', as: 'orderItems' });
OrderItem.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });

// 订单与评价
Order.hasMany(Review, { foreignKey: 'order_id', as: 'reviews' });
Review.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });

// 角色与权限
Role.belongsToMany(Permission, { 
  through: RolePermission, 
  foreignKey: 'role_id', 
  otherKey: 'permission_id',
  as: 'permissions'
});
Permission.belongsToMany(Role, { 
  through: RolePermission, 
  foreignKey: 'permission_id', 
  otherKey: 'role_id',
  as: 'roles'
});

// 管理员与角色
AdminUser.belongsTo(Role, { foreignKey: 'role_id', as: 'role' });
Role.hasMany(AdminUser, { foreignKey: 'role_id', as: 'adminUsers' });

// 折扣相关关联关系
// 折扣与商品（多对多）
Discount.belongsToMany(Product, {
  through: ProductDiscount,
  foreignKey: 'discount_id',
  otherKey: 'product_id',
  as: 'products'
});
Product.belongsToMany(Discount, {
  through: ProductDiscount,
  foreignKey: 'product_id',
  otherKey: 'discount_id',
  as: 'discounts'
});

// 折扣与分类（多对多）
Discount.belongsToMany(Category, {
  through: CategoryDiscount,
  foreignKey: 'discount_id',
  otherKey: 'category_id',
  as: 'categories'
});
Category.belongsToMany(Discount, {
  through: CategoryDiscount,
  foreignKey: 'category_id',
  otherKey: 'discount_id',
  as: 'discounts'
});

// 用户折扣使用记录
User.hasMany(UserDiscountUsage, { foreignKey: 'user_id', as: 'discountUsages' });
UserDiscountUsage.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

Discount.hasMany(UserDiscountUsage, { foreignKey: 'discount_id', as: 'usages' });
UserDiscountUsage.belongsTo(Discount, { foreignKey: 'discount_id', as: 'discount' });

Order.hasMany(UserDiscountUsage, { foreignKey: 'order_id', as: 'discountUsages' });
UserDiscountUsage.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });

// 测试数据库连接
async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.log('⚠️  服务将继续启动，但数据库功能可能不可用');
    console.log('💡 请检查数据库配置或确保MySQL服务正在运行');
  }
}

testConnection();

module.exports = {
  sequelize,
  User,
  AdminUser,
  Category,
  Product,
  CartItem,
  Cart: CartItem, // 添加Cart别名，指向CartItem模型
  Address,
  Order,
  OrderItem,
  Review,
  Banner,
  Role,
  Permission,
  RolePermission,
  Setting,
  // 折扣相关模型
  Discount,
  ProductDiscount,
  CategoryDiscount,
  UserDiscountUsage
};