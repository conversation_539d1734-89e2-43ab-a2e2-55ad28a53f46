// 退货功能API测试和优化脚本
const axios = require('axios');

// API配置
const API_CONFIG = {
  KOA_BASE_URL: 'http://localhost:4000/api',      // 小程序端API
  EXPRESS_BASE_URL: 'http://localhost:8081/api',  // 管理后台API
  TEST_TOKEN: '', // 测试用的JWT token
  ADMIN_TOKEN: '' // 管理员token
};

// 测试数据
const TEST_DATA = {
  testUserId: 1,
  testOrderId: 1,
  testReturnData: {
    orderId: 1,
    returnType: 2,
    returnReason: '商品质量问题',
    returnDescription: '收到的茶叶有异味，包装破损',
    contactPhone: '13800138000',
    returnImages: [
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg'
    ],
    returnItems: [
      {
        orderItemId: 1,
        quantity: 1,
        reason: '商品质量问题'
      }
    ]
  }
};

class ReturnAPITester {
  constructor() {
    this.testResults = [];
    this.errors = [];
  }

  // 记录测试结果
  logResult(testName, success, message, data = null) {
    const result = {
      testName,
      success,
      message,
      timestamp: new Date().toISOString(),
      data
    };
    
    this.testResults.push(result);
    
    if (success) {
      console.log(`✅ ${testName}: ${message}`);
    } else {
      console.log(`❌ ${testName}: ${message}`);
      this.errors.push(result);
    }
  }

  // 发送HTTP请求
  async makeRequest(method, url, data = null, headers = {}) {
    try {
      const config = {
        method,
        url,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      };

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return { success: true, data: response.data, status: response.status };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        status: error.response?.status || 500
      };
    }
  }

  // 测试小程序端API
  async testFrontendAPIs() {
    console.log('\n🔍 测试小程序端API...\n');

    // 1. 测试检查订单是否可退货
    const checkResult = await this.makeRequest(
      'GET',
      `${API_CONFIG.KOA_BASE_URL}/front/return/check/${TEST_DATA.testOrderId}`,
      null,
      { Authorization: `Bearer ${API_CONFIG.TEST_TOKEN}` }
    );

    this.logResult(
      '检查订单可退货状态',
      checkResult.success,
      checkResult.success ? '订单状态检查成功' : checkResult.error,
      checkResult.data
    );

    // 2. 测试获取退货配置
    const settingsResult = await this.makeRequest(
      'GET',
      `${API_CONFIG.KOA_BASE_URL}/front/return/settings`
    );

    this.logResult(
      '获取退货配置',
      settingsResult.success,
      settingsResult.success ? '退货配置获取成功' : settingsResult.error,
      settingsResult.data
    );

    // 3. 测试提交退货申请
    const submitResult = await this.makeRequest(
      'POST',
      `${API_CONFIG.KOA_BASE_URL}/front/return/submit`,
      TEST_DATA.testReturnData,
      { Authorization: `Bearer ${API_CONFIG.TEST_TOKEN}` }
    );

    this.logResult(
      '提交退货申请',
      submitResult.success,
      submitResult.success ? '退货申请提交成功' : submitResult.error,
      submitResult.data
    );

    // 4. 测试获取用户退货列表
    const listResult = await this.makeRequest(
      'GET',
      `${API_CONFIG.KOA_BASE_URL}/front/return/list?page=1&limit=10`,
      null,
      { Authorization: `Bearer ${API_CONFIG.TEST_TOKEN}` }
    );

    this.logResult(
      '获取用户退货列表',
      listResult.success,
      listResult.success ? '退货列表获取成功' : listResult.error,
      listResult.data
    );

    return this.testResults.filter(r => r.testName.includes('小程序') || 
      ['检查订单可退货状态', '获取退货配置', '提交退货申请', '获取用户退货列表'].includes(r.testName));
  }

  // 测试管理后台API
  async testAdminAPIs() {
    console.log('\n🔍 测试管理后台API...\n');

    // 1. 测试获取退货申请列表
    const listResult = await this.makeRequest(
      'GET',
      `${API_CONFIG.EXPRESS_BASE_URL}/admin/return?page=1&limit=10`,
      null,
      { Authorization: `Bearer ${API_CONFIG.ADMIN_TOKEN}` }
    );

    this.logResult(
      '管理后台-获取退货列表',
      listResult.success,
      listResult.success ? '管理后台退货列表获取成功' : listResult.error,
      listResult.data
    );

    // 2. 测试获取退货统计
    const statsResult = await this.makeRequest(
      'GET',
      `${API_CONFIG.EXPRESS_BASE_URL}/admin/return/statistics`,
      null,
      { Authorization: `Bearer ${API_CONFIG.ADMIN_TOKEN}` }
    );

    this.logResult(
      '管理后台-获取退货统计',
      statsResult.success,
      statsResult.success ? '退货统计获取成功' : statsResult.error,
      statsResult.data
    );

    // 3. 测试审核退货申请（如果有测试数据）
    if (listResult.success && listResult.data?.data?.list?.length > 0) {
      const testReturnId = listResult.data.data.list[0].id;
      
      const approveResult = await this.makeRequest(
        'PUT',
        `${API_CONFIG.EXPRESS_BASE_URL}/admin/return/approve/${testReturnId}`,
        {
          approved: true,
          adminRemark: 'API测试审核通过'
        },
        { Authorization: `Bearer ${API_CONFIG.ADMIN_TOKEN}` }
      );

      this.logResult(
        '管理后台-审核退货申请',
        approveResult.success,
        approveResult.success ? '退货申请审核成功' : approveResult.error,
        approveResult.data
      );
    }

    return this.testResults.filter(r => r.testName.includes('管理后台'));
  }

  // 测试数据一致性
  async testDataConsistency() {
    console.log('\n🔍 测试数据一致性...\n');

    // 这里可以添加数据一致性检查
    // 比如检查退货申请和订单的关联关系
    // 检查退货金额是否超过订单金额等

    this.logResult(
      '数据一致性检查',
      true,
      '数据一致性检查通过（需要实际数据验证）'
    );
  }

  // 性能测试
  async performanceTest() {
    console.log('\n🔍 进行性能测试...\n');

    const startTime = Date.now();
    
    // 并发请求测试
    const promises = [];
    for (let i = 0; i < 10; i++) {
      promises.push(
        this.makeRequest('GET', `${API_CONFIG.KOA_BASE_URL}/front/return/settings`)
      );
    }

    const results = await Promise.all(promises);
    const endTime = Date.now();
    const duration = endTime - startTime;

    const successCount = results.filter(r => r.success).length;
    
    this.logResult(
      '并发性能测试',
      successCount === 10,
      `10个并发请求，成功${successCount}个，耗时${duration}ms`,
      { successCount, totalRequests: 10, duration }
    );
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始退货功能API测试...\n');

    try {
      // 检查服务器连接
      await this.checkServerConnection();
      
      // 运行各项测试
      await this.testFrontendAPIs();
      await this.testAdminAPIs();
      await this.testDataConsistency();
      await this.performanceTest();

      // 生成测试报告
      this.generateReport();

    } catch (error) {
      console.error('测试过程中发生错误:', error);
    }
  }

  // 检查服务器连接
  async checkServerConnection() {
    console.log('🔍 检查服务器连接状态...\n');

    // 检查Koa服务器
    const koaResult = await this.makeRequest('GET', `${API_CONFIG.KOA_BASE_URL}/front/return/settings`);
    this.logResult(
      'Koa服务器连接',
      koaResult.success,
      koaResult.success ? 'Koa服务器连接正常' : 'Koa服务器连接失败'
    );

    // 检查Express服务器
    const expressResult = await this.makeRequest('GET', `${API_CONFIG.EXPRESS_BASE_URL}/admin/return/statistics`);
    this.logResult(
      'Express服务器连接',
      expressResult.success || expressResult.status === 401, // 401表示需要认证，但服务器是可达的
      expressResult.success ? 'Express服务器连接正常' : 
        (expressResult.status === 401 ? 'Express服务器连接正常（需要认证）' : 'Express服务器连接失败')
    );
  }

  // 生成测试报告
  generateReport() {
    console.log('\n📊 测试报告\n');
    console.log('='.repeat(50));

    const totalTests = this.testResults.length;
    const successTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - successTests;

    console.log(`总测试数: ${totalTests}`);
    console.log(`成功: ${successTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${((successTests / totalTests) * 100).toFixed(2)}%`);

    if (this.errors.length > 0) {
      console.log('\n❌ 失败的测试:');
      this.errors.forEach(error => {
        console.log(`  - ${error.testName}: ${error.message}`);
      });
    }

    console.log('\n✅ 测试完成！');
  }
}

// 导出测试类
module.exports = ReturnAPITester;

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const tester = new ReturnAPITester();
  tester.runAllTests();
}
