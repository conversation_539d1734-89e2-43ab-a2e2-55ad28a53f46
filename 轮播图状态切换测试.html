<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图状态切换功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            color: #1f2937;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: #10b981;
            margin-bottom: 10px;
            font-size: 24px;
        }

        .test-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .test-section h3 {
            color: #374151;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #10b981;
            color: white;
        }

        .btn-primary:hover {
            background: #059669;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .result.success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
        }

        .result.error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }

        .result.info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            color: #0369a1;
        }

        .banner-list {
            display: grid;
            gap: 15px;
            margin-top: 15px;
        }

        .banner-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .banner-image {
            width: 80px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid #d1d5db;
        }

        .banner-info {
            flex: 1;
        }

        .banner-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 4px;
        }

        .banner-id {
            font-size: 12px;
            color: #6b7280;
        }

        .banner-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.enabled {
            background: #dcfce7;
            color: #166534;
        }

        .status-badge.disabled {
            background: #fee2e2;
            color: #dc2626;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .switch.enabled {
            background: #10b981;
        }

        .switch::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .switch.enabled::before {
            transform: translateX(26px);
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 轮播图状态切换功能测试</h1>
            <p>测试轮播图的获取和状态切换功能</p>
        </div>

        <div class="test-section">
            <h3>📋 1. 获取轮播图列表</h3>
            <button class="btn btn-primary" onclick="fetchBanners()">获取轮播图列表</button>
            <button class="btn btn-secondary" onclick="clearResults()">清空结果</button>
            <div id="fetchResult" class="result info" style="display: none;"></div>
            <div id="bannerList" class="banner-list"></div>
        </div>

        <div class="test-section">
            <h3>🔄 2. 状态切换测试</h3>
            <p style="color: #6b7280; margin-bottom: 15px;">
                先获取轮播图列表，然后点击轮播图右侧的开关来测试状态切换功能
            </p>
            <div id="statusResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 3. 功能验证</h3>
            <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; border: 1px solid #bbf7d0;">
                <h4 style="color: #166534; margin-bottom: 10px;">✅ 预期效果：</h4>
                <ul style="color: #14532d; padding-left: 20px;">
                    <li>能够获取所有轮播图（包括禁用的）</li>
                    <li>点击开关能够切换轮播图状态</li>
                    <li>禁用的轮播图不会从列表中消失</li>
                    <li>状态切换后立即显示最新状态</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081/api/admin';
        
        // 获取轮播图列表
        async function fetchBanners() {
            const resultDiv = document.getElementById('fetchResult');
            const bannerListDiv = document.getElementById('bannerList');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在获取轮播图列表...';
            
            try {
                const response = await fetch(`${API_BASE}/banner`);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 获取成功！共 ${data.data.length} 个轮播图\n\n${JSON.stringify(data, null, 2)}`;
                    
                    // 渲染轮播图列表
                    renderBannerList(data.data);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取失败：${data.message}`;
                    bannerListDiv.innerHTML = '';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败：${error.message}`;
                bannerListDiv.innerHTML = '';
            }
        }
        
        // 渲染轮播图列表
        function renderBannerList(banners) {
            const bannerListDiv = document.getElementById('bannerList');
            
            if (!banners || banners.length === 0) {
                bannerListDiv.innerHTML = '<p style="text-align: center; color: #6b7280;">暂无轮播图数据</p>';
                return;
            }
            
            bannerListDiv.innerHTML = banners.map(banner => `
                <div class="banner-item" id="banner-${banner.id}">
                    <img src="${banner.image_url || '/placeholder.jpg'}" alt="${banner.title}" class="banner-image" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA4MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zMCAzMEg1MFYzNUgzMFYzMFoiIGZpbGw9IiNEOUQ5RDkiLz4KPC9zdmc+'">
                    <div class="banner-info">
                        <div class="banner-title">${banner.title}</div>
                        <div class="banner-id">ID: ${banner.id} | 排序: ${banner.sort_order}</div>
                    </div>
                    <div class="banner-status">
                        <span class="status-badge ${banner.status === 1 ? 'enabled' : 'disabled'}">
                            ${banner.status === 1 ? '启用' : '禁用'}
                        </span>
                        <div class="switch ${banner.status === 1 ? 'enabled' : ''}" 
                             onclick="toggleBannerStatus(${banner.id}, ${banner.status})">
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // 切换轮播图状态
        async function toggleBannerStatus(bannerId, currentStatus) {
            const resultDiv = document.getElementById('statusResult');
            const bannerItem = document.getElementById(`banner-${bannerId}`);
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = `正在切换轮播图 ${bannerId} 的状态...`;
            
            // 添加加载状态
            bannerItem.classList.add('loading');
            
            try {
                const newStatus = currentStatus === 1 ? 0 : 1;
                const response = await fetch(`${API_BASE}/banner/status/${bannerId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ status: newStatus })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 状态切换成功！轮播图 ${bannerId} 已${newStatus === 1 ? '启用' : '禁用'}`;
                    
                    // 刷新轮播图列表以显示最新状态
                    setTimeout(() => {
                        fetchBanners();
                    }, 500);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 状态切换失败：${data.message}`;
                    bannerItem.classList.remove('loading');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败：${error.message}`;
                bannerItem.classList.remove('loading');
            }
        }
        
        // 清空结果
        function clearResults() {
            document.getElementById('fetchResult').style.display = 'none';
            document.getElementById('statusResult').style.display = 'none';
            document.getElementById('bannerList').innerHTML = '';
        }
        
        // 页面加载完成后自动获取轮播图列表
        window.addEventListener('load', function() {
            setTimeout(() => {
                fetchBanners();
            }, 1000);
        });
    </script>
</body>
</html>
