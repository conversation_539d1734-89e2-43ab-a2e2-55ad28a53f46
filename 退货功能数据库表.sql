-- 退货功能相关数据库表

-- 1. 退货申请表
CREATE TABLE `return_requests` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '退货申请ID',
  `return_no` varchar(50) NOT NULL COMMENT '退货单号',
  `order_id` bigint NOT NULL COMMENT '原订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '原订单号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `return_type` tinyint NOT NULL DEFAULT '1' COMMENT '退货类型(1:仅退款 2:退货退款 3:换货)',
  `return_reason` varchar(200) NOT NULL COMMENT '退货原因',
  `return_description` text COMMENT '退货详细说明',
  `return_amount` decimal(10,2) NOT NULL COMMENT '申请退款金额',
  `return_quantity` int NOT NULL DEFAULT '1' COMMENT '退货数量',
  `return_images` json COMMENT '退货凭证图片',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `return_address` text COMMENT '退货地址',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '退货状态(0:待审核 1:审核通过 2:审核拒绝 3:待寄回 4:已寄回 5:验收中 6:验收通过 7:验收不通过 8:退款完成 9:已取消)',
  `admin_remark` text COMMENT '管理员备注',
  `refuse_reason` varchar(500) COMMENT '拒绝原因',
  `return_express_company` varchar(50) COMMENT '退货快递公司',
  `return_express_no` varchar(50) COMMENT '退货快递单号',
  `return_express_time` datetime COMMENT '退货寄出时间',
  `receive_time` datetime COMMENT '商家收货时间',
  `inspect_time` datetime COMMENT '验收时间',
  `refund_time` datetime COMMENT '退款时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_return_no` (`return_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退货申请表';

-- 2. 退货商品明细表
CREATE TABLE `return_items` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '退货商品ID',
  `return_id` bigint NOT NULL COMMENT '退货申请ID',
  `order_item_id` bigint NOT NULL COMMENT '原订单商品ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_image` varchar(500) COMMENT '商品图片',
  `product_price` decimal(10,2) NOT NULL COMMENT '商品单价',
  `return_quantity` int NOT NULL COMMENT '退货数量',
  `return_amount` decimal(10,2) NOT NULL COMMENT '退货金额',
  `return_reason` varchar(200) COMMENT '退货原因',
  `product_condition` tinyint COMMENT '商品状态(1:完好 2:轻微瑕疵 3:严重损坏)',
  `inspect_result` tinyint COMMENT '验收结果(1:通过 2:不通过)',
  `inspect_remark` text COMMENT '验收备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_return_id` (`return_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_order_item_id` (`order_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退货商品明细表';

-- 3. 退货状态记录表
CREATE TABLE `return_status_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `return_id` bigint NOT NULL COMMENT '退货申请ID',
  `status` tinyint NOT NULL COMMENT '状态',
  `status_text` varchar(50) NOT NULL COMMENT '状态描述',
  `operator_type` tinyint NOT NULL COMMENT '操作者类型(1:用户 2:管理员 3:系统)',
  `operator_id` bigint COMMENT '操作者ID',
  `operator_name` varchar(50) COMMENT '操作者姓名',
  `remark` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_return_id` (`return_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退货状态记录表';

-- 4. 退款记录表
CREATE TABLE `refund_records` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '退款记录ID',
  `refund_no` varchar(50) NOT NULL COMMENT '退款单号',
  `return_id` bigint NOT NULL COMMENT '退货申请ID',
  `order_id` bigint NOT NULL COMMENT '原订单ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `refund_type` tinyint NOT NULL COMMENT '退款方式(1:原路退回 2:余额退款 3:线下退款)',
  `refund_status` tinyint NOT NULL DEFAULT '0' COMMENT '退款状态(0:待退款 1:退款中 2:退款成功 3:退款失败)',
  `refund_channel` varchar(50) COMMENT '退款渠道',
  `transaction_id` varchar(100) COMMENT '第三方交易号',
  `refund_time` datetime COMMENT '退款时间',
  `success_time` datetime COMMENT '退款成功时间',
  `fail_reason` varchar(500) COMMENT '退款失败原因',
  `operator_id` bigint COMMENT '操作员ID',
  `operator_name` varchar(50) COMMENT '操作员姓名',
  `remark` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_refund_no` (`refund_no`),
  KEY `idx_return_id` (`return_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_refund_status` (`refund_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退款记录表';

-- 5. 更新订单表，添加退货相关字段
ALTER TABLE `orders` ADD COLUMN `can_return` tinyint NOT NULL DEFAULT '1' COMMENT '是否可退货(0:不可退 1:可退货)' AFTER `remark`;
ALTER TABLE `orders` ADD COLUMN `return_deadline` datetime COMMENT '退货截止时间' AFTER `can_return`;
ALTER TABLE `orders` ADD COLUMN `has_return` tinyint NOT NULL DEFAULT '0' COMMENT '是否有退货申请(0:无 1:有)' AFTER `return_deadline`;

-- 6. 创建退货配置表
CREATE TABLE `return_settings` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `setting_key` varchar(50) NOT NULL COMMENT '配置键',
  `setting_value` text NOT NULL COMMENT '配置值',
  `setting_desc` varchar(200) COMMENT '配置描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退货配置表';

-- 插入默认退货配置
INSERT INTO `return_settings` (`setting_key`, `setting_value`, `setting_desc`) VALUES
('return_deadline_days', '7', '退货申请期限（天）'),
('return_reasons', '["质量问题", "商品描述不符", "收到商品破损", "商品缺件", "不喜欢/不合适", "其他原因"]', '退货原因选项'),
('return_address', '{"name": "心洁茗茶客服", "phone": "************", "address": "福建省福州市仓山区心洁茗茶仓储中心", "zipcode": "350000"}', '退货地址信息'),
('auto_approve_amount', '100', '自动审核通过的金额上限'),
('return_shipping_fee', '10', '退货运费'),
('inspect_timeout_hours', '72', '验收超时时间（小时）');
