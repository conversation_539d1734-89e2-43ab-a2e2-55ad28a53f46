import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Upload,
  message,
  Popconfirm,
  Image,
  Space,
  Tag
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import request from '../utils/request';
import axios from 'axios';

const { TextArea } = Input;

const BannerList = () => {
  const [banners, setBanners] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingBanner, setEditingBanner] = useState(null);
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);

  // 获取轮播图列表
  const fetchBanners = async (currentPage = page, currentPageSize = pageSize) => {
    setLoading(true);
    try {
      const response = await request.get('/admin/banner', {
        params: {
          page: currentPage,
          pageSize: currentPageSize
        }
      });
      if (response.success) {
        setBanners(response.data.list || response.data);
        setTotal(response.data.total || response.data.length || 0);
      }
    } catch (error) {
      message.error('获取轮播图列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBanners();
  }, []);

  // 确保表单实例正确初始化
  useEffect(() => {
    if (form && modalVisible) {
      // 表单已连接到 Modal 中的 Form 组件
    }
  }, [form, modalVisible]);

  // 监听分页变化
  useEffect(() => {
    fetchBanners(page, pageSize);
  }, [page, pageSize]);

  // 上传图片 - 修复版本
  const uploadImage = async (file) => {
    const formData = new FormData();
    formData.append('image', file); // 字段名必须和后端一致
    formData.append('type', 'banners'); // 注意：是banners不是banner
    
    try {
      console.log('准备上传文件:', {
        name: file.name,
        size: file.size,
        type: file.type
      });
      
      // 使用标准的上传接口
      const response = await axios.post('/api/admin/upload/image', formData, {
        withCredentials: true,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
        }
      });
      
      console.log('上传响应:', response);
      
      if (response.data && response.data.success) {
        setImageUrl(response.data.data.url);
        // 安全地设置表单值
        try {
          form.setFieldsValue({ imageUrl: response.data.data.url });
        } catch (error) {
          console.warn('设置表单值失败，可能组件已卸载:', error);
        }
        message.success('图片上传成功');
        return response.data.data.url;
      } else {
        throw new Error(response.data?.message || '上传失败');
      }
    } catch (error) {
      console.error('图片上传失败:', error);
      message.error('图片上传失败: ' + (error.response?.data?.message || error.message));
      return null;
    }
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    setUploading(true);
    try {
      const imageUrlValue = form.getFieldValue('imageUrl');
      const sortOrderNum = Number(values.sortOrder);
      const bannerData = {
        title: values.title,
        description: values.description,
        image_url: imageUrlValue,
        link_url: values.linkUrl || '',
        sort_order: isNaN(sortOrderNum) || sortOrderNum < 1 ? 1 : sortOrderNum,
        status: values.status ? 1 : 0
      };
      console.log('提交参数', bannerData);
      if (!bannerData.title || !bannerData.image_url) {
        message.error('标题和图片不能为空');
        setUploading(false);
        return;
      }
      if (!bannerData.sort_order || bannerData.sort_order < 1) {
        message.error('排序号必须为大于0的数字');
        setUploading(false);
        return;
      }
      if (editingBanner) {
        await request.put(`/admin/banner/${editingBanner.id}`, bannerData);
        message.success('轮播图更新成功');
      } else {
        await request.post('/admin/banner', bannerData);
        message.success('轮播图添加成功');
      }
      setModalVisible(false);
      form.resetFields();
      setEditingBanner(null);
      setImageUrl('');
      fetchBanners();
    } catch (error) {
      message.error('操作失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setUploading(false);
    }
  };

  // 删除轮播图
  const handleDelete = async (id) => {
    try {
      await request.delete(`/admin/banner/${id}`);
      message.success('删除成功');
      fetchBanners();
    } catch (error) {
      message.error('删除失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 编辑轮播图
  const handleEdit = (record) => {
    setEditingBanner(record);
    setImageUrl(record.image_url || '');
    form.setFieldsValue({
      title: record.title,
      description: record.description,
      imageUrl: record.image_url,
      linkUrl: record.link_url,
      sortOrder: record.sort_order,
      status: record.status === 1
    });
    setModalVisible(true);
  };

  // 表格列定义 - 优化布局，确保操作列可见
  const columns = [
    {
      title: '轮播图信息',
      key: 'banner_info',
      width: 320,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Image
            width={60}
            height={45}
            src={record.image_url}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            style={{ objectFit: 'cover', borderRadius: '6px', border: '1px solid #f0f0f0' }}
          />
          <div style={{ flex: 1, minWidth: 0 }}>
            <div
              style={{
                fontWeight: '600',
                fontSize: '14px',
                color: '#262626',
                marginBottom: '4px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
              title={record.title}
            >
              {record.title}
            </div>
            <div style={{
              fontSize: '12px',
              color: '#8c8c8c',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
            title={record.description}
            >
              {record.description || '暂无描述'}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '链接地址',
      dataIndex: 'link_url',
      key: 'link_url',
      width: 200,
      render: (url) => (
        <div style={{
          fontSize: '12px',
          color: url ? '#1890ff' : '#8c8c8c',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}
        title={url}
        >
          {url || '无链接'}
        </div>
      ),
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80,
      align: 'center',
      render: (order) => (
        <span style={{
          fontWeight: '600',
          color: '#595959'
        }}>
          {order}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      align: 'center',
      render: (status) => (
        <Tag color={status === 1 ? 'green' : 'red'} size="small">
          {status === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 140,
      align: 'center',
      render: (date) => (
        <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
          {new Date(date).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      align: 'center',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            style={{ padding: '4px 8px' }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个轮播图吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              style={{ padding: '4px 8px' }}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title="轮播图管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingBanner(null);
              form.resetFields();
              setImageUrl('');
              setModalVisible(true);
            }}
          >
            添加轮播图
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={banners}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1000 }}
          pagination={{
            current: page,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条记录`,
            showLessItems: true,
            onChange: (page, pageSize) => {
              setPage(page);
              setPageSize(pageSize);
            },
          }}
        />
      </Card>

      <Modal
        title={editingBanner ? '编辑轮播图' : '添加轮播图'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingBanner(null);
          form.resetFields();
          setImageUrl('');
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            status: true,
            sortOrder: 1
          }}
        >
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input placeholder="请输入轮播图标题" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={3} placeholder="请输入轮播图描述" />
          </Form.Item>
          <Form.Item
            name="image"
            label="图片"
            rules={[
              {
                required: !editingBanner,
                message: '请上传图片'
              }
            ]}
          >
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={() => false}
              accept="image/*"
              onChange={async (info) => {
                console.log('Upload change:', info);
                
                if (info.file.status === 'removed') {
                  setImageUrl('');
                  // 安全地设置表单值
                  try {
                    form.setFieldsValue({ imageUrl: '' });
                  } catch (error) {
                    console.warn('设置表单值失败，可能组件已卸载:', error);
                  }
                  return;
                }
                
                // 只处理新上传的文件
                if (info.fileList.length > 0) {
                  const file = info.fileList[info.fileList.length - 1];
                  if (file.originFileObj) {
                    console.log('开始上传文件:', file.originFileObj);
                    await uploadImage(file.originFileObj);
                  }
                }
              }}
              fileList={imageUrl ? [{ 
                uid: '-1', 
                name: '已上传图片', 
                status: 'done', 
                url: imageUrl,
                thumbUrl: imageUrl
              }] : []}
              showUploadList={{ 
                showRemoveIcon: true,
                showPreviewIcon: true
              }}
            >
              {!imageUrl && (
                <div>
                  <UploadOutlined />
                  <div style={{ marginTop: 8 }}>上传图片</div>
                </div>
              )}
            </Upload>
          </Form.Item>
          <Form.Item
            name="imageUrl"
            label="图片URL"
            rules={[{ required: true, message: '请上传图片' }]}
            hidden
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="linkUrl"
            label="链接地址"
          >
            <Input placeholder="请输入链接地址（可选）" />
          </Form.Item>
          <Form.Item
            name="sortOrder"
            label="排序"
            rules={[
              { required: true, message: '请输入排序号' },
              { type: 'number', min: 1, message: '排序号必须大于0', transform: v => Number(v) }
            ]}
          >
            <Input type="number" placeholder="数字越小越靠前" min={1} />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
          >
            <Input type="checkbox" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={uploading}>
                {editingBanner ? '更新' : '添加'}
              </Button>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingBanner(null);
                  form.resetFields();
                  setImageUrl('');
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
);
};

export default BannerList;