const Router = require('@koa/router');
const authRoutes = require('./auth');
const adminRoutes = require('./admin');
const roleRoutes = require('./role');
const userRoutes = require('./user');
const productRoutes = require('./product');
const orderRoutes = require('./order');
const categoryRoutes = require('./category');
const bannerRoutes = require('./banner');
const statisticsRoutes = require('./statistics');
const settingsRoutes = require('./settings');
const uploadRoutes = require('./upload');
const cacheRoutes = require('./cache');
const reviewRoutes = require('./review');
const discountRoutes = require('./discount');

const router = new Router();

console.log('【路由挂载】admin: /auth');
// 管理员认证路由
router.use('/auth', authRoutes.routes(), authRoutes.allowedMethods());

console.log('【路由挂载】admin: /admin');
// 管理员管理路由
router.use('/admin', adminRoutes.routes(), adminRoutes.allowedMethods());

console.log('【路由挂载】admin: /role');
// 角色管理路由
router.use('/role', roleRoutes.routes(), roleRoutes.allowedMethods());

// 用户管理路由
router.use('/user', userRoutes.routes(), userRoutes.allowedMethods());

// 商品管理路由
router.use('/product', productRoutes.routes(), productRoutes.allowedMethods());

// 订单管理路由
router.use('/order', orderRoutes.routes(), orderRoutes.allowedMethods());

// 分类管理路由
router.use('/category', categoryRoutes.routes(), categoryRoutes.allowedMethods());

console.log('【路由挂载】admin: /banner');
// 轮播图管理路由
router.use('/banner', bannerRoutes.routes(), bannerRoutes.allowedMethods());

// 统计分析路由
router.use('/stats', statisticsRoutes.routes(), statisticsRoutes.allowedMethods());

// 系统设置路由
router.use('/settings', settingsRoutes.routes(), settingsRoutes.allowedMethods());

// 文件上传路由
router.use('/upload', uploadRoutes.routes(), uploadRoutes.allowedMethods());

// 缓存管理路由
router.use('/cache', cacheRoutes.routes(), cacheRoutes.allowedMethods());

// 评论管理路由
router.use('/review', reviewRoutes.routes(), reviewRoutes.allowedMethods());

console.log('【路由挂载】admin: /discount');
// 折扣管理路由
router.use('/discount', discountRoutes.routes(), discountRoutes.allowedMethods());

module.exports = router;