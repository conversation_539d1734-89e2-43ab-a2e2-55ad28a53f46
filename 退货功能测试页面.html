<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退货功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: #10b981;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .test-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .test-section h3 {
            color: #374151;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #10b981;
            color: white;
        }

        .btn-primary:hover {
            background: #059669;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .result.success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
        }

        .result.error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }

        .result.info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            color: #0369a1;
        }

        .api-list {
            display: grid;
            gap: 10px;
            margin-top: 15px;
        }

        .api-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: #f9fafb;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .api-method {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .api-method.get { background: #10b981; }
        .api-method.post { background: #3b82f6; }
        .api-method.put { background: #f59e0b; }

        .api-path {
            font-family: monospace;
            font-size: 13px;
            color: #374151;
            flex: 1;
        }

        .api-desc {
            color: #6b7280;
            font-size: 13px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .status-card {
            padding: 15px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }

        .status-number {
            font-size: 24px;
            font-weight: bold;
            color: #10b981;
            margin-bottom: 5px;
        }

        .status-label {
            font-size: 14px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 退货功能测试</h1>
            <p>测试心洁茶叶商城退货管理功能</p>
        </div>

        <div class="test-section">
            <h3>📊 1. API接口测试</h3>
            <div class="api-list">
                <div class="api-item">
                    <span class="api-method get">GET</span>
                    <span class="api-path">/api/admin/return/statistics</span>
                    <span class="api-desc">获取退货统计数据</span>
                    <button class="btn btn-primary" onclick="testAPI('GET', '/api/admin/return/statistics')">测试</button>
                </div>
                <div class="api-item">
                    <span class="api-method get">GET</span>
                    <span class="api-path">/api/admin/return</span>
                    <span class="api-desc">获取退货申请列表</span>
                    <button class="btn btn-primary" onclick="testAPI('GET', '/api/admin/return?page=1&limit=10')">测试</button>
                </div>
                <div class="api-item">
                    <span class="api-method get">GET</span>
                    <span class="api-path">/api/admin/return/detail/1</span>
                    <span class="api-desc">获取退货申请详情</span>
                    <button class="btn btn-primary" onclick="testAPI('GET', '/api/admin/return/detail/1')">测试</button>
                </div>
            </div>
            <div style="margin-top: 15px;">
                <button class="btn btn-secondary" onclick="testAllAPIs()">批量测试所有API</button>
                <button class="btn btn-secondary" onclick="testServerConnection()">测试服务器连接</button>
            </div>
            <div id="apiResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📈 2. 测试数据统计</h3>
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-number">5</div>
                    <div class="status-label">退货申请总数</div>
                </div>
                <div class="status-card">
                    <div class="status-number">5</div>
                    <div class="status-label">退货商品数</div>
                </div>
                <div class="status-card">
                    <div class="status-number">5</div>
                    <div class="status-label">状态日志数</div>
                </div>
                <div class="status-card">
                    <div class="status-number">1</div>
                    <div class="status-label">退款记录数</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>✅ 3. 功能完成状态</h3>
            <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; border: 1px solid #bbf7d0;">
                <h4 style="color: #166534; margin-bottom: 10px;">✅ 已完成功能：</h4>
                <ul style="color: #14532d; padding-left: 20px;">
                    <li>✅ 数据库表结构创建完成</li>
                    <li>✅ 测试数据创建完成</li>
                    <li>✅ Express API路由配置完成</li>
                    <li>✅ 管理后台前端组件完成</li>
                    <li>✅ 小程序端页面开发完成</li>
                    <li>✅ 权限验证中间件配置完成</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 4. 下一步测试计划</h3>
            <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border: 1px solid #fbbf24;">
                <h4 style="color: #92400e; margin-bottom: 10px;">🔧 需要测试的功能：</h4>
                <ul style="color: #78350f; padding-left: 20px;">
                    <li>🔧 管理后台退货页面访问测试</li>
                    <li>🔧 退货申请审核功能测试</li>
                    <li>🔧 退货状态流转测试</li>
                    <li>🔧 退款处理功能测试</li>
                    <li>🔧 小程序端退货申请测试</li>
                    <li>🔧 数据统计和导出功能测试</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081';
        
        // 测试API接口
        async function testAPI(method, path) {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = `正在测试 ${method} ${path}...`;
            
            try {
                const response = await fetch(`${API_BASE}${path}`, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include' // 包含cookies
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ API测试成功！\n\n响应数据：\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ API测试失败：\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败：${error.message}\n\n可能的原因：\n1. 服务器未启动\n2. 需要登录认证\n3. 数据库连接问题`;
            }
        }

        // 批量测试所有API
        async function testAllAPIs() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '开始批量测试所有API...\n\n';

            const apis = [
                { method: 'GET', path: '/api/admin/return/statistics', name: '获取退货统计' },
                { method: 'GET', path: '/api/admin/return?page=1&limit=10', name: '获取退货列表' },
                { method: 'GET', path: '/api/admin/return/detail/1', name: '获取退货详情' }
            ];

            let results = [];
            
            for (const api of apis) {
                try {
                    const response = await fetch(`${API_BASE}${api.path}`, {
                        method: api.method,
                        headers: { 'Content-Type': 'application/json' },
                        credentials: 'include'
                    });
                    
                    const data = await response.json();
                    results.push({
                        name: api.name,
                        success: response.ok,
                        status: response.status,
                        data: data
                    });
                } catch (error) {
                    results.push({
                        name: api.name,
                        success: false,
                        error: error.message
                    });
                }
            }

            // 显示结果
            let resultText = '批量API测试结果：\n\n';
            results.forEach(result => {
                resultText += `${result.success ? '✅' : '❌'} ${result.name}\n`;
                if (result.success) {
                    resultText += `   状态码: ${result.status}\n`;
                    if (result.data && result.data.data) {
                        resultText += `   数据: ${JSON.stringify(result.data.data, null, 2).substring(0, 200)}...\n`;
                    }
                } else {
                    resultText += `   错误: ${result.error || '请求失败'}\n`;
                }
                resultText += '\n';
            });

            const successCount = results.filter(r => r.success).length;
            resultText += `\n总结: ${successCount}/${results.length} 个API测试成功`;

            resultDiv.className = successCount === results.length ? 'result success' : 'result error';
            resultDiv.textContent = resultText;
        }

        // 测试服务器连接
        async function testServerConnection() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试服务器连接...\n\n';

            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE}/api/admin/return/statistics`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include'
                });
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                let resultText = '服务器连接测试结果：\n\n';
                resultText += `✅ Express服务器 (管理后台)\n`;
                resultText += `   状态码: ${response.status}\n`;
                resultText += `   响应时间: ${responseTime}ms\n`;
                
                if (response.status === 401) {
                    resultText += `   说明: 需要登录认证（正常）\n`;
                } else if (response.ok) {
                    resultText += `   说明: 连接成功\n`;
                }
                
                resultText += `\n💡 提示：\n`;
                resultText += `1. 如果看到401错误，说明需要先登录管理后台\n`;
                resultText += `2. 请访问 http://localhost:8081 登录后再测试\n`;
                resultText += `3. 数据库表和测试数据已创建完成\n`;

                resultDiv.className = 'result success';
                resultDiv.textContent = resultText;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 服务器连接失败：${error.message}\n\n解决建议：\n1. 检查Express服务器是否启动\n2. 确认端口8081是否可用\n3. 检查防火墙设置`;
            }
        }

        // 页面加载完成后自动测试连接
        window.addEventListener('load', function() {
            console.log('退货功能测试页面加载完成');
            setTimeout(() => {
                testServerConnection();
            }, 1000);
        });
    </script>
</body>
</html>
