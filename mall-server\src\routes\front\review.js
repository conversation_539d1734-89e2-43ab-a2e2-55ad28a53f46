const Router = require('@koa/router');
const { createReview, getProductReviews, getUserReviews, deleteReview, checkCanReview } = require('../../controllers/front/review');
const auth = require('../../middleware/auth');

const router = new Router({
  prefix: '/api/front/review'
});

// 发表商品评价
router.post('/', auth, createReview);

// 获取商品评价列表
router.get('/product/:productId', getProductReviews);

// 获取用户评价列表
router.get('/user', auth, getUserReviews);

// 删除评价
router.delete('/:id', auth, deleteReview);

// 检查用户是否可以评价商品
router.get('/check/:productId', auth, checkCanReview);

module.exports = router; 