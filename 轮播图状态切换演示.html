<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图状态切换功能演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            color: #1f2937;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: #10b981;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .header p {
            color: #6b7280;
            font-size: 16px;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .demo-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .demo-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            padding: 15px;
            border-radius: 8px;
        }

        .old-title {
            background: #fee2e2;
            color: #dc2626;
        }

        .new-title {
            background: #dcfce7;
            color: #166534;
        }

        /* 模拟表格样式 */
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .demo-table th,
        .demo-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .demo-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .demo-table img {
            width: 60px;
            height: 45px;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        /* 旧版Tag样式 */
        .old-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .old-tag.enabled {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .old-tag.disabled {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        /* 新版Switch样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .switch.enabled {
            background: #10b981;
        }

        .switch::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .switch.enabled::before {
            transform: translateX(36px);
        }

        .switch-text {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: 10px;
            font-weight: 500;
            color: white;
        }

        .switch .switch-text.on {
            left: 6px;
            display: none;
        }

        .switch .switch-text.off {
            right: 6px;
        }

        .switch.enabled .switch-text.on {
            display: block;
        }

        .switch.enabled .switch-text.off {
            display: none;
        }

        .improvements {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-top: 30px;
        }

        .improvements h3 {
            color: #10b981;
            margin-bottom: 20px;
            font-size: 24px;
            text-align: center;
        }

        .improvement-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .improvement-item {
            padding: 20px;
            background: #f8fffe;
            border-radius: 12px;
            border-left: 4px solid #10b981;
        }

        .improvement-item h4 {
            color: #047857;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .improvement-item p {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.5;
        }

        .before-after {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }

        .before, .after {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .before {
            background: #fee2e2;
            color: #dc2626;
        }

        .after {
            background: #dcfce7;
            color: #166534;
        }

        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .container {
                padding: 10px;
            }
            
            .improvement-list {
                grid-template-columns: 1fr;
            }
        }

        .action-buttons {
            text-align: center;
            margin-top: 20px;
        }

        .btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 0 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.2s;
        }

        .btn-edit {
            background: #1890ff;
            color: white;
        }

        .btn-edit:hover {
            background: #40a9ff;
        }

        .btn-delete {
            background: #ff4d4f;
            color: white;
        }

        .btn-delete:hover {
            background: #ff7875;
        }

        .demo-message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: #10b981;
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .demo-message.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 轮播图状态切换功能优化</h1>
            <p>对比优化前后的状态管理方式，展示Switch开关的交互优势</p>
        </div>

        <div class="comparison">
            <!-- 优化前 -->
            <div class="demo-section">
                <div class="demo-title old-title">❌ 优化前：静态Tag显示</div>
                
                <table class="demo-table">
                    <thead>
                        <tr>
                            <th>图片</th>
                            <th>标题</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDUiIHZpZXdCb3g9IjAgMCA2MCA0NSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQ1IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEgzNVYyNUgyMFYyMFoiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTIwIDI3SDQwVjMySDE5VjI3WiIgZmlsbD0iI0Q5RDlEOSIvPgo8L3N2Zz4K" alt="轮播图"></td>
                            <td>春茶上新</td>
                            <td><span class="old-tag enabled">启用</span></td>
                            <td>
                                <button class="btn btn-edit">编辑</button>
                                <button class="btn btn-delete">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDUiIHZpZXdCb3g9IjAgMCA2MCA0NSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQ1IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEgzNVYyNUgyMFYyMFoiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTIwIDI3SDQwVjMySDE5VjI3WiIgZmlsbD0iI0Q5RDlEOSIvPgo8L3N2Zz4K" alt="轮播图"></td>
                            <td>夏日清香</td>
                            <td><span class="old-tag disabled">禁用</span></td>
                            <td>
                                <button class="btn btn-edit">编辑</button>
                                <button class="btn btn-delete">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="margin-top: 15px; padding: 15px; background: #fef2f2; border-radius: 8px;">
                    <h4 style="color: #dc2626; margin-bottom: 8px;">存在问题：</h4>
                    <ul style="color: #7f1d1d; font-size: 14px; padding-left: 20px;">
                        <li>状态只能查看，无法直接操作</li>
                        <li>需要进入编辑页面才能修改状态</li>
                        <li>操作步骤繁琐，效率低下</li>
                        <li>禁用商品可能会从列表消失</li>
                    </ul>
                </div>
            </div>

            <!-- 优化后 -->
            <div class="demo-section">
                <div class="demo-title new-title">✅ 优化后：可交互Switch开关</div>
                
                <table class="demo-table">
                    <thead>
                        <tr>
                            <th>图片</th>
                            <th>标题</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDUiIHZpZXdCb3g9IjAgMCA2MCA0NSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQ1IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEgzNVYyNUgyMFYyMFoiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTIwIDI3SDQwVjMySDE5VjI3WiIgZmlsbD0iI0Q5RDlEOSIvPgo8L3N2Zz4K" alt="轮播图"></td>
                            <td>春茶上新</td>
                            <td>
                                <div class="switch enabled" onclick="toggleSwitch(this, '春茶上新')">
                                    <span class="switch-text on">启用</span>
                                    <span class="switch-text off">禁用</span>
                                </div>
                            </td>
                            <td>
                                <button class="btn btn-edit">编辑</button>
                                <button class="btn btn-delete">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDUiIHZpZXdCb3g9IjAgMCA2MCA0NSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQ1IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEgzNVYyNUgyMFYyMFoiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTIwIDI3SDQwVjMySDE5VjI3WiIgZmlsbD0iI0Q5RDlEOSIvPgo8L3N2Zz4K" alt="轮播图"></td>
                            <td>夏日清香</td>
                            <td>
                                <div class="switch" onclick="toggleSwitch(this, '夏日清香')">
                                    <span class="switch-text on">启用</span>
                                    <span class="switch-text off">禁用</span>
                                </div>
                            </td>
                            <td>
                                <button class="btn btn-edit">编辑</button>
                                <button class="btn btn-delete">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="margin-top: 15px; padding: 15px; background: #f0fdf4; border-radius: 8px;">
                    <h4 style="color: #166534; margin-bottom: 8px;">优化效果：</h4>
                    <ul style="color: #14532d; font-size: 14px; padding-left: 20px;">
                        <li>点击开关即可切换状态</li>
                        <li>无需进入编辑页面</li>
                        <li>操作简便，效率提升</li>
                        <li>禁用商品仍然显示在列表中</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="improvements">
            <h3>🚀 核心改进点</h3>
            <div class="improvement-list">
                <div class="improvement-item">
                    <h4>🎯 交互方式优化</h4>
                    <p>从静态的Tag标签改为可交互的Switch开关，用户可以直接点击切换状态。</p>
                    <div class="before-after">
                        <span class="before">静态Tag</span>
                        <span>→</span>
                        <span class="after">Switch开关</span>
                    </div>
                </div>

                <div class="improvement-item">
                    <h4>⚡ 操作效率提升</h4>
                    <p>从需要进入编辑页面修改状态，简化为列表页面直接一键切换。</p>
                    <div class="before-after">
                        <span class="before">多步操作</span>
                        <span>→</span>
                        <span class="after">一键切换</span>
                    </div>
                </div>

                <div class="improvement-item">
                    <h4>📊 数据保留</h4>
                    <p>禁用的轮播图不会从列表中消失，而是保持显示状态，便于管理。</p>
                    <div class="before-after">
                        <span class="before">可能消失</span>
                        <span>→</span>
                        <span class="after">始终显示</span>
                    </div>
                </div>

                <div class="improvement-item">
                    <h4>🎨 视觉反馈</h4>
                    <p>Switch开关提供更直观的状态反馈，开启/关闭状态一目了然。</p>
                    <div class="before-after">
                        <span class="before">颜色区分</span>
                        <span>→</span>
                        <span class="after">开关+文字</span>
                    </div>
                </div>

                <div class="improvement-item">
                    <h4>🔄 即时更新</h4>
                    <p>状态切换后立即保存到数据库，并自动刷新列表显示最新状态。</p>
                    <div class="before-after">
                        <span class="before">手动刷新</span>
                        <span>→</span>
                        <span class="after">自动更新</span>
                    </div>
                </div>

                <div class="improvement-item">
                    <h4>💬 用户反馈</h4>
                    <p>操作成功后显示明确的提示信息，让用户了解操作结果。</p>
                    <div class="before-after">
                        <span class="before">无反馈</span>
                        <span>→</span>
                        <span class="after">成功提示</span>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 30px; background: white; border-radius: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <h3 style="color: #10b981; margin-bottom: 15px;">🎉 优化总结</h3>
            <p style="color: #6b7280; font-size: 16px; line-height: 1.6; max-width: 800px; margin: 0 auto;">
                通过将静态的Tag标签改为可交互的Switch开关，大大提升了轮播图状态管理的用户体验。
                用户现在可以直接在列表页面一键切换状态，无需进入编辑页面，操作更加便捷高效。
                同时确保禁用的轮播图不会消失，便于后续管理。
            </p>
            <div style="margin-top: 20px;">
                <span style="background: #dcfce7; color: #166534; padding: 8px 16px; border-radius: 20px; font-weight: 600;">
                    ✅ 交互优化 ✅ 效率提升 ✅ 数据保留
                </span>
            </div>
        </div>
    </div>

    <div class="demo-message" id="demoMessage">
        轮播图状态已更新！
    </div>

    <script>
        function toggleSwitch(element, title) {
            const isEnabled = element.classList.contains('enabled');
            
            if (isEnabled) {
                element.classList.remove('enabled');
                showMessage(`${title} 已禁用`);
            } else {
                element.classList.add('enabled');
                showMessage(`${title} 已启用`);
            }
        }

        function showMessage(text) {
            const message = document.getElementById('demoMessage');
            message.textContent = text;
            message.classList.add('show');
            
            setTimeout(() => {
                message.classList.remove('show');
            }, 2000);
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            setTimeout(() => {
                showMessage('点击Switch开关体验状态切换功能！');
            }, 1000);
        });
    </script>
</body>
</html>
