-- 退货功能数据库表创建脚本
USE xinjie_mall;

-- 1. 创建退货申请表
CREATE TABLE IF NOT EXISTS return_requests (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  return_no VARCHAR(50) NOT NULL UNIQUE COMMENT '退货单号',
  order_id BIGINT NOT NULL COMMENT '订单ID',
  order_no VARCHAR(50) NOT NULL COMMENT '订单号',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  return_type TINYINT NOT NULL DEFAULT 2 COMMENT '退货类型：1仅退款 2退货退款 3换货',
  return_reason VARCHAR(200) NOT NULL COMMENT '退货原因',
  return_description TEXT COMMENT '退货详细说明',
  return_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '退货金额',
  contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
  return_images TEXT COMMENT '退货凭证图片JSON',
  status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0待审核 1审核通过 2审核拒绝 3待寄回 4已寄回 5验收中 6验收通过 7验收不通过 8退款完成 9已取消',
  admin_remark TEXT COMMENT '管理员备注',
  refuse_reason VARCHAR(500) COMMENT '拒绝原因',
  express_company VARCHAR(100) COMMENT '快递公司',
  express_no VARCHAR(100) COMMENT '快递单号',
  receive_time DATETIME COMMENT '收货时间',
  inspect_time DATETIME COMMENT '验收时间',
  refund_time DATETIME COMMENT '退款时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_return_no (return_no),
  INDEX idx_order_id (order_id),
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退货申请表';

-- 2. 创建退货商品明细表
CREATE TABLE IF NOT EXISTS return_items (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  return_id BIGINT NOT NULL COMMENT '退货申请ID',
  order_item_id BIGINT NOT NULL COMMENT '订单商品ID',
  product_id BIGINT NOT NULL COMMENT '商品ID',
  product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
  product_image VARCHAR(500) COMMENT '商品图片',
  product_price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
  return_quantity INT NOT NULL DEFAULT 1 COMMENT '退货数量',
  return_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '退货金额',
  reason VARCHAR(200) COMMENT '退货原因',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_return_id (return_id),
  INDEX idx_product_id (product_id),
  INDEX idx_order_item_id (order_item_id),
  FOREIGN KEY (return_id) REFERENCES return_requests(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退货商品明细表';

-- 3. 创建退货状态记录表
CREATE TABLE IF NOT EXISTS return_status_logs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  return_id BIGINT NOT NULL COMMENT '退货申请ID',
  status TINYINT NOT NULL COMMENT '状态',
  status_text VARCHAR(50) NOT NULL COMMENT '状态文本',
  operator_type TINYINT NOT NULL DEFAULT 3 COMMENT '操作者类型：1用户 2管理员 3系统',
  operator_id BIGINT COMMENT '操作者ID',
  operator_name VARCHAR(100) COMMENT '操作者姓名',
  remark TEXT COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_return_id (return_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (return_id) REFERENCES return_requests(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退货状态记录表';

-- 4. 创建退款记录表
CREATE TABLE IF NOT EXISTS refund_records (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  refund_no VARCHAR(50) NOT NULL UNIQUE COMMENT '退款单号',
  return_id BIGINT NOT NULL COMMENT '退货申请ID',
  order_id BIGINT NOT NULL COMMENT '订单ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  refund_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  refund_type TINYINT NOT NULL DEFAULT 2 COMMENT '退款方式：1原路退回 2余额退款 3线下退款',
  refund_status TINYINT NOT NULL DEFAULT 1 COMMENT '退款状态：1处理中 2成功 3失败',
  refund_time DATETIME COMMENT '退款发起时间',
  success_time DATETIME COMMENT '退款成功时间',
  fail_reason VARCHAR(500) COMMENT '失败原因',
  operator_id BIGINT COMMENT '操作员ID',
  operator_name VARCHAR(100) COMMENT '操作员姓名',
  remark TEXT COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_refund_no (refund_no),
  INDEX idx_return_id (return_id),
  INDEX idx_user_id (user_id),
  INDEX idx_refund_status (refund_status),
  FOREIGN KEY (return_id) REFERENCES return_requests(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款记录表';

-- 5. 创建退货配置表
CREATE TABLE IF NOT EXISTS return_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  setting_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
  setting_value TEXT COMMENT '配置值',
  setting_desc VARCHAR(200) COMMENT '配置描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退货配置表';

-- 6. 为订单表添加退货相关字段（如果不存在）
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS can_return TINYINT DEFAULT 1 COMMENT '是否可退货：0否 1是',
ADD COLUMN IF NOT EXISTS return_deadline DATETIME COMMENT '退货截止时间',
ADD COLUMN IF NOT EXISTS has_return TINYINT DEFAULT 0 COMMENT '是否有退货申请：0否 1是';

-- 7. 插入默认退货配置
INSERT IGNORE INTO return_settings (setting_key, setting_value, setting_desc) VALUES
('return_days', '7', '退货天数限制'),
('return_reasons', '["质量问题","商品描述不符","收到商品破损","商品缺件","不喜欢/不合适","其他原因"]', '退货原因选项'),
('return_address', '{"name":"心洁茶叶","phone":"************","address":"福建省福州市仓山区茶叶批发市场","zipcode":"350000"}', '退货地址信息'),
('auto_approve', '0', '是否自动审核：0否 1是'),
('refund_to_balance', '1', '是否退款到余额：0否 1是');

-- 8. 创建统计视图
CREATE OR REPLACE VIEW v_return_statistics AS
SELECT 
  DATE(created_at) as stat_date,
  status,
  COUNT(*) as request_count,
  SUM(return_amount) as total_amount,
  AVG(return_amount) as avg_amount
FROM return_requests 
GROUP BY DATE(created_at), status;

-- 9. 创建用户退货统计视图
CREATE OR REPLACE VIEW v_user_return_stats AS
SELECT 
  user_id,
  COUNT(*) as total_returns,
  COUNT(CASE WHEN status = 8 THEN 1 END) as completed_returns,
  SUM(return_amount) as total_return_amount,
  MAX(created_at) as last_return_time
FROM return_requests 
GROUP BY user_id;

-- 显示创建结果
SELECT '退货功能数据库表创建完成！' as message;
SHOW TABLES LIKE '%return%';
SHOW TABLES LIKE '%refund%';
