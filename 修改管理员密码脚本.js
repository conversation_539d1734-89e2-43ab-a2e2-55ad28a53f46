#!/usr/bin/env node

/**
 * 修改管理员密码脚本
 * 使用方法: node 修改管理员密码脚本.js
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const readline = require('readline');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '', // 请填入您的数据库密码
  database: 'xinjie_mall'
};

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 提示用户输入
function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

// 隐藏密码输入
function hiddenQuestion(prompt) {
  return new Promise((resolve) => {
    process.stdout.write(prompt);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let password = '';
    process.stdin.on('data', function(char) {
      char = char + '';
      
      switch(char) {
        case '\n':
        case '\r':
        case '\u0004':
          process.stdin.setRawMode(false);
          process.stdin.pause();
          process.stdout.write('\n');
          resolve(password);
          break;
        case '\u0003':
          process.exit();
          break;
        case '\u007f': // 退格键
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          password += char;
          process.stdout.write('*');
          break;
      }
    });
  });
}

// 验证密码强度
function validatePassword(password) {
  const errors = [];
  
  if (password.length < 6) {
    errors.push('密码长度至少6位');
  }
  
  if (password.length > 20) {
    errors.push('密码长度不能超过20位');
  }
  
  if (!/\d/.test(password)) {
    errors.push('密码必须包含至少一个数字');
  }
  
  return errors;
}

// 主函数
async function main() {
  let connection;
  
  try {
    console.log('🔧 心洁茶叶商城 - 管理员密码修改工具\n');
    
    // 连接数据库
    console.log('正在连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功\n');
    
    // 查询所有管理员
    const [admins] = await connection.execute(
      'SELECT id, username, real_name FROM admin_users WHERE status = 1'
    );
    
    if (admins.length === 0) {
      console.log('❌ 没有找到可用的管理员账户');
      return;
    }
    
    // 显示管理员列表
    console.log('📋 当前管理员列表:');
    admins.forEach((admin, index) => {
      console.log(`${index + 1}. ${admin.username} (${admin.real_name || '未设置姓名'})`);
    });
    console.log('');
    
    // 选择管理员
    const choice = await question('请选择要修改密码的管理员编号: ');
    const adminIndex = parseInt(choice) - 1;
    
    if (adminIndex < 0 || adminIndex >= admins.length) {
      console.log('❌ 无效的选择');
      return;
    }
    
    const selectedAdmin = admins[adminIndex];
    console.log(`\n已选择管理员: ${selectedAdmin.username}\n`);
    
    // 输入新密码
    let newPassword;
    let confirmPassword;
    
    do {
      newPassword = await hiddenQuestion('请输入新密码: ');
      
      // 验证密码强度
      const errors = validatePassword(newPassword);
      if (errors.length > 0) {
        console.log('❌ 密码不符合要求:');
        errors.forEach(error => console.log(`   - ${error}`));
        console.log('');
        continue;
      }
      
      confirmPassword = await hiddenQuestion('请确认新密码: ');
      
      if (newPassword !== confirmPassword) {
        console.log('❌ 两次输入的密码不一致，请重新输入\n');
      }
    } while (newPassword !== confirmPassword);
    
    // 确认修改
    const confirm = await question('\n确认修改密码? (y/N): ');
    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      console.log('❌ 操作已取消');
      return;
    }
    
    // 加密密码
    console.log('\n正在加密密码...');
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    // 更新数据库
    console.log('正在更新数据库...');
    const [result] = await connection.execute(
      'UPDATE admin_users SET password = ?, updated_at = NOW() WHERE id = ?',
      [hashedPassword, selectedAdmin.id]
    );
    
    if (result.affectedRows > 0) {
      console.log('✅ 密码修改成功!');
      console.log(`\n管理员: ${selectedAdmin.username}`);
      console.log('新密码已生效，请使用新密码登录管理后台。');
    } else {
      console.log('❌ 密码修改失败');
    }
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 解决建议:');
      console.log('1. 检查MySQL服务是否启动');
      console.log('2. 检查数据库连接配置是否正确');
      console.log('3. 确认数据库用户名和密码');
    } else if (error.code === 'ER_NO_SUCH_TABLE') {
      console.log('\n💡 解决建议:');
      console.log('1. 确认数据库名称是否正确');
      console.log('2. 检查admin_users表是否存在');
      console.log('3. 运行数据库初始化脚本');
    }
  } finally {
    if (connection) {
      await connection.end();
    }
    rl.close();
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n👋 程序已退出');
  process.exit(0);
});

// 运行主函数
main().catch(console.error);
