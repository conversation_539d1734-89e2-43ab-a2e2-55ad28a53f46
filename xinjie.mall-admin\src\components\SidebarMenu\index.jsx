import React from 'react';
import { Menu } from 'antd';
import {
  DashboardOutlined,
  ShoppingOutlined,
  TagsOutlined,
  OrderedListOutlined,
  BarChartOutlined,
  UserOutlined,
  SettingOutlined,
  PictureOutlined,
  ShopOutlined,
  FileTextOutlined,
  TeamOutlined,
  ToolOutlined,
  DollarOutlined,
  CrownOutlined,
  ShareAltOutlined,
  UsergroupAddOutlined,
  GiftOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';

const menuKeyToPath = {
  dashboard: '/',
  banner: '/banner',
  'product-list': '/product',
  'product-add': '/product/add',
  category: '/category',
  'discount-list': '/discount',
  order: '/order',
  stats: '/stats',
  user: '/user',
  'user-recharge': '/user/recharge',
  'user-member': '/user/member',
  // 分销路径映射全部启用
  'distribution-list': '/distribution/distributors',
  'distribution-stats': '/distribution/stats',
  'distribution-commission': '/distribution/commission',
  'distribution-config': '/distribution/config',
  'settings-basic': '/settings/basic',
  'settings-payment': '/settings/payment',
  'settings-shipping': '/settings/shipping',
  'settings-sms': '/settings/sms',
  'settings-email': '/settings/email',
};

const pathToMenuKey = Object.entries(menuKeyToPath).reduce((acc, [k, v]) => {
  acc[v] = k;
  return acc;
}, {});

const SidebarMenu = () => {
  const navigate = useNavigate();
  const location = useLocation();
  // 处理高亮
  const selectedKey = pathToMenuKey[location.pathname] || 'dashboard';
  // 处理展开（如settings、product、user）
  let openKeys = [];
  if (selectedKey.startsWith('settings')) openKeys.push('settings');
  if (selectedKey.startsWith('product')) openKeys.push('product');
  if (selectedKey.startsWith('user')) openKeys.push('user-management');
  // 逐步启用分销菜单展开逻辑
  if (selectedKey.startsWith('distribution')) openKeys.push('distribution-management');

  // 品牌Logo区域 - 专业办公主题设计
  const BrandLogo = () => (
    <div style={{
      padding: '24px 20px',
      textAlign: 'center',
      borderBottom: '1px solid rgba(255,255,255,0.15)',
      marginBottom: '20px',
      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
      position: 'relative'
    }}>
      {/* 主Logo */}
      <div style={{
        fontSize: '28px',
        marginBottom: '12px',
        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))'
      }}>
        🍃
      </div>

      {/* 品牌名称 */}
      <div style={{
        color: '#fff',
        fontSize: '16px',
        fontWeight: '600',
        letterSpacing: '0.5px',
        marginBottom: '4px',
        textShadow: '0 1px 2px rgba(0,0,0,0.2)'
      }}>
        心洁茗茶
      </div>

      {/* 副标题 */}
      <div style={{
        color: 'rgba(255,255,255,0.8)',
        fontSize: '11px',
        fontWeight: '500',
        letterSpacing: '0.5px'
      }}>
        管理后台
      </div>

      {/* 装饰线 */}
      <div style={{
        width: '30px',
        height: '2px',
        background: 'rgba(255,255,255,0.4)',
        margin: '12px auto 0',
        borderRadius: '1px'
      }} />
    </div>
  );

  // 菜单分组标题组件 - 专业办公主题
  const MenuGroupTitle = ({ title, icon }) => (
    <div style={{
      padding: '12px 20px 8px 20px',
      color: 'rgba(255,255,255,0.7)',
      fontSize: '11px',
      fontWeight: '700',
      textTransform: 'uppercase',
      letterSpacing: '1px',
      marginTop: '20px',
      marginBottom: '8px',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      borderLeft: '3px solid rgba(255,255,255,0.3)',
      background: 'rgba(255,255,255,0.05)',
      position: 'relative'
    }}>
      {icon && <span style={{ fontSize: '12px', opacity: 0.8 }}>{icon}</span>}
      <span>{title}</span>
      <div style={{
        flex: 1,
        height: '1px',
        background: 'linear-gradient(90deg, rgba(255,255,255,0.3), transparent)',
        marginLeft: '8px'
      }} />
    </div>
  );

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined style={{ fontSize: '16px' }} />,
      label: (
        <span style={{ fontSize: '14px', fontWeight: '500' }}>
          系统概览
        </span>
      ),
    },
    {
      type: 'divider',
      style: {
        margin: '8px 16px',
        borderColor: 'rgba(255,255,255,0.1)'
      }
    },
    {
      key: 'content-group',
      label: <MenuGroupTitle title="内容管理" icon="📝" />,
      type: 'group',
      children: [
        {
          key: 'banner',
          icon: <PictureOutlined style={{ fontSize: '16px' }} />,
          label: (
            <span style={{ fontSize: '14px', fontWeight: '500' }}>
              轮播图管理
            </span>
          ),
        },
        {
          key: 'product',
          icon: <ShopOutlined style={{ fontSize: '16px' }} />,
          label: (
            <span style={{ fontSize: '14px', fontWeight: '500' }}>
              商品管理
            </span>
          ),
          children: [
            {
              key: 'product-list',
              icon: <ShoppingOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  商品列表
                </span>
              ),
            },
            {
              key: 'category',
              icon: <TagsOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  分类管理
                </span>
              ),
            },
            {
              key: 'discount-list',
              icon: <TagsOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  折扣管理
                </span>
              ),
            },
          ],
        },
      ]
    },
    {
      key: 'business-group',
      label: <MenuGroupTitle title="业务管理" icon="💼" />,
      type: 'group',
      children: [
        {
          key: 'order',
          icon: <FileTextOutlined style={{ fontSize: '16px' }} />,
          label: (
            <span style={{ fontSize: '14px', fontWeight: '500' }}>
              订单管理
            </span>
          ),
        },
        {
          key: 'user-management',
          icon: <TeamOutlined style={{ fontSize: '16px' }} />,
          label: (
            <span style={{ fontSize: '14px', fontWeight: '500' }}>
              用户管理
            </span>
          ),
          children: [
            {
              key: 'user',
              icon: <UserOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  用户列表
                </span>
              ),
            },
            {
              key: 'user-recharge',
              icon: <DollarOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  充值管理
                </span>
              ),
            },
            {
              key: 'user-member',
              icon: <CrownOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  会员管理
                </span>
              ),
            },
          ],
        },
        {
          key: 'distribution-management',
          icon: <ShareAltOutlined style={{ fontSize: '16px' }} />,
          label: (
            <span style={{ fontSize: '14px', fontWeight: '500' }}>
              分销管理
            </span>
          ),
          children: [
            {
              key: 'distribution-list',
              icon: <UsergroupAddOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  分销商管理
                </span>
              ),
            },
            {
              key: 'distribution-commission',
              icon: <DollarOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  佣金管理
                </span>
              ),
            },
            {
              key: 'distribution-stats',
              icon: <BarChartOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  分销统计
                </span>
              ),
            },
            {
              key: 'distribution-config',
              icon: <SettingOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  分销配置
                </span>
              ),
            },
            // {
            //   key: 'distribution-config',
            //   icon: <SettingOutlined style={{ fontSize: '14px' }} />,
            //   label: (
            //     <span style={{ fontSize: '13px' }}>
            //       分销配置
            //     </span>
            //   ),
            // },
          ],
        },
        {
          key: 'stats',
          icon: <BarChartOutlined style={{ fontSize: '16px' }} />,
          label: (
            <span style={{ fontSize: '14px', fontWeight: '500' }}>
              数据统计
            </span>
          ),
        },
      ]
    },
    {
      key: 'system-group',
      label: <MenuGroupTitle title="系统设置" icon="⚙️" />,
      type: 'group',
      children: [
        {
          key: 'settings',
          icon: <ToolOutlined style={{ fontSize: '16px' }} />,
          label: (
            <span style={{ fontSize: '14px', fontWeight: '500' }}>
              系统配置
            </span>
          ),
          children: [
            {
              key: 'settings-basic',
              icon: <SettingOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  基本设置
                </span>
              ),
            },
            {
              key: 'settings-payment',
              icon: <OrderedListOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  支付设置
                </span>
              ),
            },
            {
              key: 'settings-shipping',
              icon: <OrderedListOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  物流设置
                </span>
              ),
            },
            {
              key: 'settings-sms',
              icon: <OrderedListOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  短信设置
                </span>
              ),
            },
            {
              key: 'settings-email',
              icon: <OrderedListOutlined style={{ fontSize: '14px' }} />,
              label: (
                <span style={{ fontSize: '13px' }}>
                  邮件设置
                </span>
              ),
            },
          ],
        },
      ]
    }
  ];

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <BrandLogo />

      <div style={{ flex: 1, overflow: 'auto' }}>
        <Menu
          mode='inline'
          style={{
            height: '100%',
            borderRight: 0,
            background: 'transparent'
          }}
          selectedKeys={[selectedKey]}
          defaultOpenKeys={['product', 'settings', 'user-management', 'distribution-management']}
          items={menuItems}
          onClick={({ key }) => {
            const path = menuKeyToPath[key];
            if (path) navigate(path);
          }}
        />
      </div>

      {/* 底部信息区域 - 专业办公主题 */}
      <div style={{
        padding: '20px 16px',
        borderTop: '1px solid rgba(255,255,255,0.1)',
        textAlign: 'center',
        background: 'rgba(255,255,255,0.05)',
        position: 'relative'
      }}>
        <div style={{
          color: 'rgba(255,255,255,0.8)',
          fontSize: '11px',
          lineHeight: '1.6'
        }}>
          <div style={{
            fontWeight: '600',
            marginBottom: '4px',
            color: 'rgba(255,255,255,0.9)'
          }}>
            © 2024 心洁茗茶
          </div>
          <div style={{
            fontSize: '10px',
            opacity: 0.8
          }}>
            管理系统 v2.0.1
          </div>
          <div style={{
            marginTop: '8px',
            fontSize: '9px',
            opacity: 0.6,
            fontStyle: 'italic'
          }}>
            "专业 · 高效 · 智能"
          </div>
        </div>
      </div>
    </div>
  );
};

export default SidebarMenu;
