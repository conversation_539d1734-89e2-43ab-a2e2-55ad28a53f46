#!/usr/bin/env node

/**
 * 测试余额记录接口
 * 验证修复后的接口是否正常工作
 */

const http = require('http');

// 测试接口函数
function testAPI(path, expectedStatus = 200) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8081,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = {
            status: res.statusCode,
            headers: res.headers,
            data: data ? JSON.parse(data) : null
          };
          resolve(result);
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.end();
  });
}

async function runTests() {
  console.log('🧪 测试余额记录接口...\n');

  const tests = [
    {
      name: '余额变动记录接口',
      path: '/api/admin/recharge/balance-records?page=1&pageSize=10',
      description: '测试获取余额变动记录'
    },
    {
      name: '充值记录接口',
      path: '/api/admin/recharge/list?page=1&pageSize=10',
      description: '测试获取充值记录'
    },
    {
      name: '充值统计接口',
      path: '/api/admin/recharge/statistics',
      description: '测试获取充值统计'
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      console.log(`📋 测试: ${test.name}`);
      console.log(`🔗 路径: ${test.path}`);
      console.log(`📝 描述: ${test.description}`);
      
      const result = await testAPI(test.path);
      
      console.log(`📊 状态码: ${result.status}`);
      
      if (result.status === 200) {
        console.log('✅ 测试通过');
        
        if (result.data && result.data.success) {
          console.log('✅ 响应格式正确');
          
          if (result.data.data) {
            console.log(`📈 数据: ${JSON.stringify(result.data.data, null, 2).substring(0, 200)}...`);
          }
        } else {
          console.log('⚠️ 响应格式异常');
          console.log(`📄 响应内容: ${JSON.stringify(result.data, null, 2).substring(0, 300)}...`);
        }
        
        passedTests++;
      } else if (result.status === 500) {
        console.log('❌ 服务器内部错误');
        
        if (result.data && result.data.message) {
          console.log(`💬 错误信息: ${result.data.message}`);
        }
        
        if (result.data && result.data.error) {
          console.log(`🔍 错误详情: ${JSON.stringify(result.data.error, null, 2)}`);
        }
      } else {
        console.log(`❌ 测试失败，状态码: ${result.status}`);
        console.log(`📄 响应内容: ${JSON.stringify(result.data, null, 2).substring(0, 300)}...`);
      }
      
    } catch (error) {
      console.log(`❌ 测试异常: ${error.message}`);
      
      if (error.code === 'ECONNREFUSED') {
        console.log('💡 提示: 管理后台服务可能未启动，请先启动服务');
        console.log('   启动命令: cd xinjie.mall-admin && npm start');
      }
    }
    
    console.log('─'.repeat(60));
  }

  console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！余额记录接口修复成功');
  } else {
    console.log('⚠️ 部分测试失败，请检查以下内容:');
    console.log('1. 管理后台服务是否正常启动');
    console.log('2. 数据库连接是否正常');
    console.log('3. balance_records表是否存在');
    console.log('4. 相关路由是否正确注册');
    
    console.log('\n🔧 修复建议:');
    console.log('1. 运行数据库修复脚本: node 修复余额记录接口.js');
    console.log('2. 重启管理后台服务: cd xinjie.mall-admin && npm restart');
    console.log('3. 检查数据库配置: xinjie.mall-admin/src/config/index.js');
  }
}

// 检查服务是否运行
async function checkService() {
  try {
    console.log('🔍 检查管理后台服务状态...');
    const result = await testAPI('/api/admin/user/profile');
    
    if (result.status === 200 || result.status === 401) {
      console.log('✅ 管理后台服务正在运行');
      return true;
    } else {
      console.log(`⚠️ 服务响应异常，状态码: ${result.status}`);
      return false;
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ 管理后台服务未启动');
      console.log('💡 请先启动服务: cd xinjie.mall-admin && npm start');
    } else {
      console.log(`❌ 服务检查失败: ${error.message}`);
    }
    return false;
  }
}

// 主函数
async function main() {
  console.log('🚀 余额记录接口测试工具\n');
  
  const serviceRunning = await checkService();
  console.log('');
  
  if (serviceRunning) {
    await runTests();
  } else {
    console.log('❌ 无法进行测试，请先确保管理后台服务正常运行');
  }
}

// 运行测试
main().catch(console.error);
