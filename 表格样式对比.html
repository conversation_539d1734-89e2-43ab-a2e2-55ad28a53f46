<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品管理表格样式对比</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            color: #1f2937;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: #10b981;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .header p {
            color: #6b7280;
            font-size: 16px;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .style-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .style-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
            padding: 10px;
            border-radius: 8px;
        }

        .clean-title {
            background: #f8f9fa;
            color: #495057;
        }

        .gradient-title {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%);
            color: #047857;
        }

        /* 纯白简洁表格样式 */
        .clean-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            background: #ffffff;
        }

        .clean-table th {
            background: #ffffff;
            border-bottom: 2px solid #f0f0f0;
            color: #1f2937;
            font-weight: 600;
            font-size: 14px;
            padding: 16px 12px;
            text-align: left;
        }

        .clean-table td {
            background: #ffffff;
            border-bottom: 1px solid #f5f5f5;
            padding: 16px 12px;
            transition: all 0.2s ease;
        }

        .clean-table tr:nth-child(even) td {
            background: #fafafa;
        }

        .clean-table tr:hover td {
            background: #f0f0f0;
        }

        /* 整体渐变表格样式 */
        .gradient-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);
            background: linear-gradient(135deg, #ffffff 0%, #f8fffe 50%, #f0fdf4 100%);
        }

        .gradient-table th {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%);
            border-bottom: 2px solid rgba(16, 185, 129, 0.2);
            color: #047857;
            font-weight: 700;
            font-size: 14px;
            padding: 16px 12px;
            text-align: left;
        }

        .gradient-table td {
            background: rgba(255, 255, 255, 0.8);
            border-bottom: 1px solid rgba(16, 185, 129, 0.1);
            padding: 16px 12px;
            transition: all 0.2s ease;
        }

        .gradient-table tr:nth-child(even) td {
            background: rgba(240, 253, 244, 0.3);
        }

        .gradient-table tr:hover td {
            background: rgba(16, 185, 129, 0.05);
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);
        }

        .product-image {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
        }

        .price {
            color: #ef4444;
            font-weight: 600;
        }

        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status.active {
            background: #dcfce7;
            color: #166534;
        }

        .status.inactive {
            background: #fee2e2;
            color: #991b1b;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            margin-right: 8px;
            transition: all 0.2s ease;
        }

        .edit-btn {
            background: #3b82f6;
            color: white;
        }

        .edit-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .delete-btn {
            background: #ef4444;
            color: white;
        }

        .delete-btn:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }

        .toggle-section {
            text-align: center;
            margin-top: 30px;
            background: white;
            padding: 20px;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .toggle-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .toggle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .toggle-btn.active {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .container {
                padding: 10px;
            }
            
            .clean-table, .gradient-table {
                font-size: 12px;
            }
            
            .clean-table th, .clean-table td,
            .gradient-table th, .gradient-table td {
                padding: 8px 6px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 商品管理表格样式优化</h1>
            <p>对比纯白简洁版 vs 整体渐变版，选择最适合的表格样式</p>
        </div>

        <div class="comparison">
            <!-- 纯白简洁版 -->
            <div class="style-section">
                <div class="style-title clean-title">✨ 纯白简洁版（推荐）</div>
                <table class="clean-table">
                    <thead>
                        <tr>
                            <th>商品信息</th>
                            <th>价格</th>
                            <th>库存</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='50' height='50' viewBox='0 0 50 50'%3E%3Crect width='50' height='50' fill='%2310b981'/%3E%3Ctext x='25' y='30' text-anchor='middle' fill='white' font-size='20'%3E🍃%3C/text%3E%3C/svg%3E" alt="花茶水仙" class="product-image">
                                    <span>花茶水仙</span>
                                </div>
                            </td>
                            <td><span class="price">¥399.00</span></td>
                            <td>200</td>
                            <td><span class="status active">上架</span></td>
                            <td>
                                <button class="action-btn edit-btn">编辑</button>
                                <button class="action-btn delete-btn">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='50' height='50' viewBox='0 0 50 50'%3E%3Crect width='50' height='50' fill='%2334d399'/%3E%3Ctext x='25' y='30' text-anchor='middle' fill='white' font-size='20'%3E🍃%3C/text%3E%3C/svg%3E" alt="和顺玉茶" class="product-image">
                                    <span>和顺玉茶</span>
                                </div>
                            </td>
                            <td><span class="price">¥299.00</span></td>
                            <td>120</td>
                            <td><span class="status active">上架</span></td>
                            <td>
                                <button class="action-btn edit-btn">编辑</button>
                                <button class="action-btn delete-btn">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='50' height='50' viewBox='0 0 50 50'%3E%3Crect width='50' height='50' fill='%236ee7b7'/%3E%3Ctext x='25' y='30' text-anchor='middle' fill='white' font-size='20'%3E🍃%3C/text%3E%3C/svg%3E" alt="古茗雅香" class="product-image">
                                    <span>古茗雅香</span>
                                </div>
                            </td>
                            <td><span class="price">¥199.00</span></td>
                            <td>0</td>
                            <td><span class="status inactive">下架</span></td>
                            <td>
                                <button class="action-btn edit-btn">编辑</button>
                                <button class="action-btn delete-btn">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 整体渐变版 -->
            <div class="style-section">
                <div class="style-title gradient-title">🌈 整体渐变版</div>
                <table class="gradient-table">
                    <thead>
                        <tr>
                            <th>商品信息</th>
                            <th>价格</th>
                            <th>库存</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='50' height='50' viewBox='0 0 50 50'%3E%3Crect width='50' height='50' fill='%2310b981'/%3E%3Ctext x='25' y='30' text-anchor='middle' fill='white' font-size='20'%3E🍃%3C/text%3E%3C/svg%3E" alt="花茶水仙" class="product-image">
                                    <span>花茶水仙</span>
                                </div>
                            </td>
                            <td><span class="price">¥399.00</span></td>
                            <td>200</td>
                            <td><span class="status active">上架</span></td>
                            <td>
                                <button class="action-btn edit-btn">编辑</button>
                                <button class="action-btn delete-btn">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='50' height='50' viewBox='0 0 50 50'%3E%3Crect width='50' height='50' fill='%2334d399'/%3E%3Ctext x='25' y='30' text-anchor='middle' fill='white' font-size='20'%3E🍃%3C/text%3E%3C/svg%3E" alt="和顺玉茶" class="product-image">
                                    <span>和顺玉茶</span>
                                </div>
                            </td>
                            <td><span class="price">¥299.00</span></td>
                            <td>120</td>
                            <td><span class="status active">上架</span></td>
                            <td>
                                <button class="action-btn edit-btn">编辑</button>
                                <button class="action-btn delete-btn">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='50' height='50' viewBox='0 0 50 50'%3E%3Crect width='50' height='50' fill='%236ee7b7'/%3E%3Ctext x='25' y='30' text-anchor='middle' fill='white' font-size='20'%3E🍃%3C/text%3E%3C/svg%3E" alt="古茗雅香" class="product-image">
                                    <span>古茗雅香</span>
                                </div>
                            </td>
                            <td><span class="price">¥199.00</span></td>
                            <td>0</td>
                            <td><span class="status inactive">下架</span></td>
                            <td>
                                <button class="action-btn edit-btn">编辑</button>
                                <button class="action-btn delete-btn">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="toggle-section">
            <h3 style="margin-bottom: 20px; color: #374151;">💡 如何切换样式</h3>
            <p style="margin-bottom: 20px; color: #6b7280;">
                默认使用纯白简洁版。如需使用渐变版，请在表格元素上添加 <code style="background: #f3f4f6; padding: 2px 6px; border-radius: 4px;">gradient-table</code> 类名
            </p>
            <button class="toggle-btn active" onclick="showClean()">使用纯白简洁版</button>
            <button class="toggle-btn" onclick="showGradient()">使用整体渐变版</button>
        </div>
    </div>

    <script>
        function showClean() {
            document.querySelectorAll('.toggle-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            alert('✅ 已选择纯白简洁版\n\n这是默认样式，无需额外配置。\n表格将显示为干净的白色背景，适合专业的管理界面。');
        }

        function showGradient() {
            document.querySelectorAll('.toggle-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            alert('🌈 已选择整体渐变版\n\n请在需要的表格元素上添加 "gradient-table" 类名：\n<Table className="gradient-table" ... />\n\n表格将显示为浅绿色渐变背景。');
        }

        // 添加悬停效果演示
        document.querySelectorAll('tr').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.cursor = 'pointer';
            });
        });
    </script>
</body>
</html>
