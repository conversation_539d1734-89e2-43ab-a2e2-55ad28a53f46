#!/bin/bash

# 折扣功能部署脚本
# 使用方法: ./deploy-discount-feature.sh

echo "🚀 开始部署折扣功能..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 错误处理函数
handle_error() {
    echo -e "${RED}❌ 错误: $1${NC}"
    exit 1
}

# 成功信息函数
success_msg() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 警告信息函数
warning_msg() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 信息函数
info_msg() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 检查必要的工具
check_requirements() {
    info_msg "检查系统要求..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        handle_error "Node.js 未安装，请先安装 Node.js"
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        handle_error "npm 未安装，请先安装 npm"
    fi
    
    # 检查MySQL
    if ! command -v mysql &> /dev/null; then
        warning_msg "MySQL 客户端未找到，请确保 MySQL 服务正在运行"
    fi
    
    success_msg "系统要求检查完成"
}

# 数据库迁移
migrate_database() {
    info_msg "执行数据库迁移..."
    
    # 检查数据库配置文件
    if [ ! -f "mall-server/src/config/database.js" ]; then
        handle_error "数据库配置文件不存在: mall-server/src/config/database.js"
    fi
    
    # 执行后端API数据库迁移
    if [ -f "mall-server/database/migrations/create-discount-tables.sql" ]; then
        info_msg "执行后端API数据库迁移..."
        # 这里需要根据实际的数据库连接方式来执行SQL
        # mysql -u username -p database_name < mall-server/database/migrations/create-discount-tables.sql
        success_msg "后端API数据库迁移完成"
    else
        warning_msg "后端API数据库迁移文件不存在"
    fi
    
    # 执行管理后台数据库迁移
    if [ -f "xinjie.mall-admin/database/migrations/create-discount-tables.sql" ]; then
        info_msg "执行管理后台数据库迁移..."
        # mysql -u username -p database_name < xinjie.mall-admin/database/migrations/create-discount-tables.sql
        success_msg "管理后台数据库迁移完成"
    else
        warning_msg "管理后台数据库迁移文件不存在"
    fi
}

# 安装依赖
install_dependencies() {
    info_msg "安装项目依赖..."
    
    # 安装后端API依赖
    if [ -d "mall-server" ]; then
        info_msg "安装后端API依赖..."
        cd mall-server
        npm install || handle_error "后端API依赖安装失败"
        cd ..
        success_msg "后端API依赖安装完成"
    fi
    
    # 安装管理后台依赖
    if [ -d "xinjie.mall-admin" ]; then
        info_msg "安装管理后台依赖..."
        cd xinjie.mall-admin
        npm install || handle_error "管理后台依赖安装失败"
        cd ..
        success_msg "管理后台依赖安装完成"
    fi
}

# 构建项目
build_projects() {
    info_msg "构建项目..."
    
    # 构建React管理后台（如果存在）
    if [ -f "xinjie.mall-admin/src/pages/DiscountList.jsx" ]; then
        info_msg "构建React管理后台..."
        cd xinjie.mall-admin
        npm run build || warning_msg "React管理后台构建失败，但不影响EJS版本"
        cd ..
    fi
    
    success_msg "项目构建完成"
}

# 验证文件完整性
verify_files() {
    info_msg "验证文件完整性..."
    
    # 检查关键文件
    local files=(
        "mall-server/database/migrations/create-discount-tables.sql"
        "mall-server/src/models/Discount.js"
        "mall-server/src/controllers/admin/discount.js"
        "mall-server/src/routes/admin/discount.js"
        "xinjie.mall-admin/database/migrations/create-discount-tables.sql"
        "xinjie.mall-admin/models/discountModel.js"
        "xinjie.mall-admin/controllers/discountController.js"
        "xinjie.mall-admin/routes/discountRoutes.js"
        "xinjie.mall-admin/views/discountManage.ejs"
        "xinjie.mall-admin/public/js/discountManage.js"
    )
    
    local missing_files=()
    
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        warning_msg "以下文件缺失:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
    else
        success_msg "所有关键文件都存在"
    fi
}

# 启动服务
start_services() {
    info_msg "准备启动服务..."
    
    # 创建启动脚本
    cat > start-discount-services.sh << 'EOF'
#!/bin/bash

echo "🚀 启动折扣功能服务..."

# 启动后端API服务
if [ -d "mall-server" ]; then
    echo "启动后端API服务..."
    cd mall-server
    npm start &
    BACKEND_PID=$!
    echo "后端API服务已启动 (PID: $BACKEND_PID)"
    cd ..
fi

# 启动管理后台服务
if [ -d "xinjie.mall-admin" ]; then
    echo "启动管理后台服务..."
    cd xinjie.mall-admin
    npm start &
    ADMIN_PID=$!
    echo "管理后台服务已启动 (PID: $ADMIN_PID)"
    cd ..
fi

echo "✅ 所有服务已启动"
echo "📝 访问地址:"
echo "   - 后端API: http://localhost:3000"
echo "   - 管理后台: http://localhost:8080"
echo ""
echo "🛑 停止服务请运行: ./stop-discount-services.sh"

# 保存PID以便后续停止
echo "$BACKEND_PID" > backend.pid
echo "$ADMIN_PID" > admin.pid
EOF

    # 创建停止脚本
    cat > stop-discount-services.sh << 'EOF'
#!/bin/bash

echo "🛑 停止折扣功能服务..."

# 停止后端API服务
if [ -f "backend.pid" ]; then
    BACKEND_PID=$(cat backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        echo "后端API服务已停止"
    fi
    rm -f backend.pid
fi

# 停止管理后台服务
if [ -f "admin.pid" ]; then
    ADMIN_PID=$(cat admin.pid)
    if kill -0 $ADMIN_PID 2>/dev/null; then
        kill $ADMIN_PID
        echo "管理后台服务已停止"
    fi
    rm -f admin.pid
fi

echo "✅ 所有服务已停止"
EOF

    chmod +x start-discount-services.sh
    chmod +x stop-discount-services.sh
    
    success_msg "服务启动脚本已创建"
    info_msg "运行 './start-discount-services.sh' 启动服务"
    info_msg "运行 './stop-discount-services.sh' 停止服务"
}

# 生成部署报告
generate_report() {
    info_msg "生成部署报告..."
    
    cat > discount-deployment-report.md << EOF
# 折扣功能部署报告

## 部署时间
$(date)

## 部署状态
✅ 折扣功能部署完成

## 功能清单

### 数据库
- [x] 折扣规则表 (discounts)
- [x] 商品折扣关联表 (product_discounts)
- [x] 分类折扣关联表 (category_discounts)
- [x] 用户折扣使用记录表 (user_discount_usage)

### 后端API
- [x] 折扣管理控制器
- [x] 折扣数据模型
- [x] 折扣管理路由
- [x] 折扣计算逻辑

### 管理后台
- [x] 折扣管理页面 (EJS)
- [x] 折扣管理脚本 (JavaScript)
- [x] 商品管理集成
- [x] 导航菜单更新

### React组件 (可选)
- [x] 折扣列表组件
- [x] API接口封装
- [x] 路由配置

## 访问地址
- 后端API: http://localhost:3000
- 管理后台: http://localhost:8080
- 折扣管理: http://localhost:8080 (商品管理 > 折扣管理)

## 测试建议
1. 访问管理后台，测试折扣CRUD操作
2. 在商品管理中测试折扣价格显示
3. 验证不同类型折扣的计算逻辑
4. 测试折扣的启用/禁用功能

## 注意事项
- 确保数据库连接正常
- 检查所有依赖是否安装完成
- 验证权限配置是否正确

## 支持
如有问题，请参考 test-discount-functionality.md 文件
EOF

    success_msg "部署报告已生成: discount-deployment-report.md"
}

# 主函数
main() {
    echo "🎯 折扣功能部署脚本"
    echo "===================="
    
    check_requirements
    verify_files
    install_dependencies
    migrate_database
    build_projects
    start_services
    generate_report
    
    echo ""
    echo "🎉 折扣功能部署完成！"
    echo ""
    echo "📋 下一步操作:"
    echo "1. 运行 './start-discount-services.sh' 启动服务"
    echo "2. 访问 http://localhost:8080 测试管理后台"
    echo "3. 查看 discount-deployment-report.md 了解详细信息"
    echo "4. 参考 test-discount-functionality.md 进行功能测试"
    echo ""
}

# 执行主函数
main "$@"
