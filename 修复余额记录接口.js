#!/usr/bin/env node

/**
 * 修复余额记录接口问题
 * 检查数据库表结构并修复相关问题
 */

const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '', // 请填入您的数据库密码
  database: 'xinjie_mall'
};

async function main() {
  let connection;
  
  try {
    console.log('🔧 修复余额记录接口问题...\n');
    
    // 连接数据库
    console.log('正在连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功\n');
    
    // 1. 检查balance_records表是否存在
    console.log('1. 检查balance_records表...');
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'balance_records'"
    );
    
    if (tables.length === 0) {
      console.log('❌ balance_records表不存在，正在创建...');
      
      // 创建balance_records表
      await connection.execute(`
        CREATE TABLE balance_records (
          id INT PRIMARY KEY AUTO_INCREMENT,
          user_id INT NOT NULL COMMENT '用户ID',
          type TINYINT NOT NULL COMMENT '变动类型(1:增加 2:减少)',
          amount DECIMAL(10,2) NOT NULL COMMENT '变动金额',
          balance_before DECIMAL(10,2) NOT NULL COMMENT '变动前余额',
          balance_after DECIMAL(10,2) NOT NULL COMMENT '变动后余额',
          source TINYINT NOT NULL COMMENT '来源(1:充值 2:消费 3:退款 4:调整)',
          source_id INT NULL COMMENT '来源ID(订单ID/充值ID等)',
          remark VARCHAR(255) NULL COMMENT '备注',
          operator_id INT NULL COMMENT '操作员ID',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          
          INDEX idx_user_id (user_id),
          INDEX idx_type (type),
          INDEX idx_source (source),
          INDEX idx_created_at (created_at),
          
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='余额变动记录表'
      `);
      
      console.log('✅ balance_records表创建成功');
    } else {
      console.log('✅ balance_records表已存在');
    }
    
    // 2. 检查recharge_records表是否存在
    console.log('\n2. 检查recharge_records表...');
    const [rechargeTables] = await connection.execute(
      "SHOW TABLES LIKE 'recharge_records'"
    );
    
    if (rechargeTables.length === 0) {
      console.log('❌ recharge_records表不存在，正在创建...');
      
      // 创建recharge_records表
      await connection.execute(`
        CREATE TABLE recharge_records (
          id INT PRIMARY KEY AUTO_INCREMENT,
          user_id INT NOT NULL COMMENT '用户ID',
          order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '充值订单号',
          amount DECIMAL(10,2) NOT NULL COMMENT '充值金额',
          bonus_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '赠送金额',
          total_amount DECIMAL(10,2) NOT NULL COMMENT '实际到账金额',
          payment_method TINYINT NOT NULL COMMENT '支付方式(1:微信 2:支付宝 3:银行卡)',
          payment_status TINYINT DEFAULT 0 COMMENT '支付状态(0:待支付 1:已支付 2:支付失败 3:已退款)',
          transaction_id VARCHAR(100) NULL COMMENT '第三方交易号',
          paid_at TIMESTAMP NULL COMMENT '支付时间',
          remark VARCHAR(255) NULL COMMENT '备注',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          
          INDEX idx_user_id (user_id),
          INDEX idx_order_no (order_no),
          INDEX idx_payment_status (payment_status),
          INDEX idx_created_at (created_at),
          
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值记录表'
      `);
      
      console.log('✅ recharge_records表创建成功');
    } else {
      console.log('✅ recharge_records表已存在');
    }
    
    // 3. 检查users表是否有balance字段
    console.log('\n3. 检查users表balance字段...');
    const [columns] = await connection.execute(
      "SHOW COLUMNS FROM users LIKE 'balance'"
    );
    
    if (columns.length === 0) {
      console.log('❌ users表缺少balance字段，正在添加...');
      
      await connection.execute(`
        ALTER TABLE users 
        ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '账户余额' AFTER phone,
        ADD COLUMN points INT DEFAULT 0 COMMENT '积分' AFTER balance,
        ADD COLUMN user_level TINYINT DEFAULT 1 COMMENT '用户等级' AFTER points
      `);
      
      console.log('✅ users表字段添加成功');
    } else {
      console.log('✅ users表balance字段已存在');
    }
    
    // 4. 插入测试数据
    console.log('\n4. 插入测试数据...');
    
    // 检查是否有测试数据
    const [balanceRecords] = await connection.execute(
      'SELECT COUNT(*) as count FROM balance_records'
    );
    
    if (balanceRecords[0].count === 0) {
      console.log('正在插入测试余额记录...');
      
      // 获取第一个用户ID
      const [users] = await connection.execute(
        'SELECT id FROM users LIMIT 1'
      );
      
      if (users.length > 0) {
        const userId = users[0].id;
        
        // 插入测试余额记录
        await connection.execute(`
          INSERT INTO balance_records (user_id, type, amount, balance_before, balance_after, source, remark) VALUES
          (?, 1, 100.00, 0.00, 100.00, 1, '测试充值记录'),
          (?, 2, 50.00, 100.00, 50.00, 2, '测试消费记录'),
          (?, 1, 200.00, 50.00, 250.00, 1, '测试充值记录2')
        `, [userId, userId, userId]);
        
        // 更新用户余额
        await connection.execute(
          'UPDATE users SET balance = 250.00 WHERE id = ?',
          [userId]
        );
        
        console.log('✅ 测试数据插入成功');
      } else {
        console.log('⚠️ 没有找到用户，跳过测试数据插入');
      }
    } else {
      console.log('✅ 余额记录已存在，跳过测试数据插入');
    }
    
    // 5. 测试查询
    console.log('\n5. 测试余额记录查询...');
    const [testRecords] = await connection.execute(`
      SELECT 
        br.*,
        u.nickname,
        u.phone,
        CASE br.type
          WHEN 1 THEN '增加'
          WHEN 2 THEN '减少'
          ELSE '未知'
        END as type_text,
        CASE br.source
          WHEN 1 THEN '充值'
          WHEN 2 THEN '消费'
          WHEN 3 THEN '退款'
          WHEN 4 THEN '后台调整'
          ELSE '未知'
        END as source_text
      FROM balance_records br 
      LEFT JOIN users u ON br.user_id = u.id 
      ORDER BY br.created_at DESC 
      LIMIT 5
    `);
    
    console.log(`✅ 查询成功，找到 ${testRecords.length} 条记录`);
    
    if (testRecords.length > 0) {
      console.log('\n📋 示例记录:');
      testRecords.forEach((record, index) => {
        console.log(`${index + 1}. 用户: ${record.nickname || '未知'}, 类型: ${record.type_text}, 金额: ${record.amount}, 来源: ${record.source_text}`);
      });
    }
    
    console.log('\n🎉 余额记录接口修复完成！');
    console.log('\n📝 修复总结:');
    console.log('1. ✅ 检查并创建了balance_records表');
    console.log('2. ✅ 检查并创建了recharge_records表');
    console.log('3. ✅ 检查并添加了users表的balance字段');
    console.log('4. ✅ 插入了测试数据');
    console.log('5. ✅ 验证了查询功能');
    
    console.log('\n🔗 现在可以测试以下接口:');
    console.log('- GET http://localhost:8081/api/admin/recharge/balance-records');
    console.log('- GET http://localhost:8081/api/admin/recharge/list');
    
  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 解决建议:');
      console.log('1. 检查MySQL服务是否启动');
      console.log('2. 检查数据库连接配置是否正确');
      console.log('3. 确认数据库用户名和密码');
    } else if (error.code === 'ER_NO_SUCH_TABLE') {
      console.log('\n💡 解决建议:');
      console.log('1. 确认数据库名称是否正确');
      console.log('2. 检查相关表是否存在');
      console.log('3. 运行数据库初始化脚本');
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行修复脚本
main().catch(console.error);
